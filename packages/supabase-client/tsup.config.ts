import { defineConfig } from 'tsup';

export default defineConfig([
  // Клиентская сборка
  {
    entry: ['src/index.ts'],
    format: ['esm', 'cjs'],
    dts: true,
    external: ['react', 'react-dom', 'next', '@react-pdf/renderer'],
    sourcemap: true,
    clean: true,
    treeshake: true,
    banner: {
      js: '"use client";',
    },
  },
  // Серверная сборка
  {
    entry: ['src/server.ts'],
    format: ['esm', 'cjs'],
    dts: true,
    external: ['react', 'react-dom', 'next'],
    sourcemap: true,
    treeshake: true,
    outDir: 'dist/server',
  },
]);
