/**
 * @file: superadmin.test.js
 * @description: Автоматизированные тесты для SuperAdmin панели PactCRM
 * @dependencies: playwright
 * @created: 2025-01-26
 */

const { test, expect } = require('@playwright/test');

// Конфигурация тестов
const BASE_URL = 'http://localhost:3003';
const TEST_CREDENTIALS = {
  email: '<EMAIL>',
  password: 'SuperAdmin123!'
};

test.describe('SuperAdmin Panel Tests', () => {
  
  test('Загрузка страницы авторизации', async ({ page }) => {
    console.log('🔍 Тест: Загрузка страницы авторизации');
    
    // Переходим на страницу авторизации
    await page.goto(`${BASE_URL}/admin/login`);
    
    // Проверяем заголовок страницы
    await expect(page).toHaveTitle(/PactCRM/);
    
    // Проверяем наличие формы авторизации
    await expect(page.locator('form')).toBeVisible();
    
    // Проверяем наличие полей email и password
    await expect(page.locator('input[type="email"]')).toBeVisible();
    await expect(page.locator('input[type="password"]')).toBeVisible();
    
    // Проверяем наличие кнопки входа
    await expect(page.locator('button[type="submit"]')).toBeVisible();
    
    console.log('✅ Страница авторизации загружена успешно');
  });

  test('Ввод тестовых данных и авторизация', async ({ page }) => {
    console.log('🔍 Тест: Ввод тестовых данных и авторизация');
    
    // Переходим на страницу авторизации
    await page.goto(`${BASE_URL}/admin/login`);
    
    // Заполняем форму
    await page.fill('input[type="email"]', TEST_CREDENTIALS.email);
    await page.fill('input[type="password"]', TEST_CREDENTIALS.password);
    
    // Нажимаем кнопку входа
    await page.click('button[type="submit"]');
    
    // Ждем перенаправления или изменения URL
    await page.waitForTimeout(3000);
    
    console.log('✅ Данные введены, попытка авторизации выполнена');
  });

  test('Загрузка дашборда', async ({ page }) => {
    console.log('🔍 Тест: Загрузка дашборда');
    
    // Переходим напрямую на дашборд
    await page.goto(`${BASE_URL}/admin/dashboard`);
    
    // Проверяем наличие основных элементов дашборда
    await expect(page.locator('h1')).toContainText('SuperAdmin Dashboard');
    
    // Проверяем наличие статистических карточек
    const statsCards = page.locator('[data-testid="stats-card"], .grid > div');
    await expect(statsCards.first()).toBeVisible();
    
    console.log('✅ Дашборд загружен успешно');
  });

  test('Проверка UI элементов дашборда', async ({ page }) => {
    console.log('🔍 Тест: Проверка UI элементов дашборда');
    
    // Переходим на дашборд
    await page.goto(`${BASE_URL}/admin/dashboard`);
    
    // Проверяем наличие боковой панели
    const sidebar = page.locator('[data-sidebar="sidebar"]');
    if (await sidebar.isVisible()) {
      console.log('✅ Боковая панель найдена');
    }
    
    // Проверяем наличие таблиц
    const tables = page.locator('table');
    if (await tables.count() > 0) {
      console.log('✅ Таблицы найдены');
    }
    
    // Проверяем наличие карточек
    const cards = page.locator('[class*="card"]');
    if (await cards.count() > 0) {
      console.log('✅ Карточки найдены');
    }
    
    console.log('✅ UI элементы проверены');
  });

  test('Проверка консоли на ошибки', async ({ page }) => {
    console.log('🔍 Тест: Проверка консоли на ошибки');
    
    const consoleErrors = [];
    
    // Слушаем ошибки консоли
    page.on('console', msg => {
      if (msg.type() === 'error') {
        consoleErrors.push(msg.text());
      }
    });
    
    // Переходим на страницы
    await page.goto(`${BASE_URL}/admin/login`);
    await page.waitForTimeout(2000);
    
    await page.goto(`${BASE_URL}/admin/dashboard`);
    await page.waitForTimeout(2000);
    
    // Выводим найденные ошибки
    if (consoleErrors.length > 0) {
      console.log('⚠️ Найдены ошибки в консоли:');
      consoleErrors.forEach(error => console.log(`  - ${error}`));
    } else {
      console.log('✅ Ошибок в консоли не найдено');
    }
  });

  test('Проверка адаптивности дизайна', async ({ page }) => {
    console.log('🔍 Тест: Проверка адаптивности дизайна');
    
    // Тестируем разные размеры экрана
    const viewports = [
      { width: 1920, height: 1080, name: 'Desktop' },
      { width: 768, height: 1024, name: 'Tablet' },
      { width: 375, height: 667, name: 'Mobile' }
    ];
    
    for (const viewport of viewports) {
      await page.setViewportSize({ width: viewport.width, height: viewport.height });
      await page.goto(`${BASE_URL}/admin/dashboard`);
      await page.waitForTimeout(1000);
      
      console.log(`✅ ${viewport.name} (${viewport.width}x${viewport.height}) - OK`);
    }
  });

  test('Тестирование упрощенных страниц', async ({ page }) => {
    console.log('🔍 Тест: Тестирование упрощенных страниц');
    
    // Тестируем упрощенную авторизацию
    await page.goto(`${BASE_URL}/admin/simple-login`);
    await expect(page.locator('h1')).toContainText('PactCRM SuperAdmin');
    console.log('✅ Упрощенная авторизация загружена');
    
    // Тестируем упрощенный дашборд
    await page.goto(`${BASE_URL}/admin/simple-dashboard`);
    await expect(page.locator('h1')).toContainText('SuperAdmin Dashboard');
    console.log('✅ Упрощенный дашборд загружен');
    
    // Тестируем тестовую страницу
    await page.goto(`${BASE_URL}/test`);
    await expect(page.locator('h1')).toContainText('SuperAdmin Test Page');
    console.log('✅ Тестовая страница загружена');
  });

});
