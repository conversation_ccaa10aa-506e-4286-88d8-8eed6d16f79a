/**
 * @file: auth-server-helpers.ts
 * @description: Серверные вспомогательные функции для аутентификации
 * @dependencies: @supabase/supabase-js
 * @created: 2025-01-26
 */

import { AuthError } from '@supabase/supabase-js'
import { createServerSupabaseClient } from './auth-config'

/**
 * Серверные функции для аутентификации
 */
export class ServerAuthService {
  /**
   * Получение сессии на сервере
   */
  async getSession() {
    try {
      const supabase = await createServerSupabaseClient()
      const { data, error } = await supabase.auth.getSession()
      return { session: data.session, error }
    } catch (error) {
      return { session: null, error: error as AuthError }
    }
  }

  /**
   * Получение пользователя на сервере
   */
  async getUser() {
    try {
      const supabase = await createServerSupabaseClient()
      const { data, error } = await supabase.auth.getUser()
      return { user: data.user, error }
    } catch (error) {
      return { user: null, error: error as AuthError }
    }
  }
}

/**
 * Экспорт экземпляра сервиса
 */
export const serverAuthService = new ServerAuthService()
