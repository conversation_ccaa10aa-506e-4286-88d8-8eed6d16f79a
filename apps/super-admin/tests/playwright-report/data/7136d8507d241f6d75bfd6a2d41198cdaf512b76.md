# Test info

- Name: SuperAdmin Panel Tests >> Загрузка страницы авторизации
- Location: /Users/<USER>/DEV/SAAS PactCRM/apps/super-admin/tests/superadmin.test.js:19:3

# Error details

```
Error: page.goto: net::ERR_ABORTED; maybe frame was detached?
Call log:
  - navigating to "http://localhost:3003/admin/login", waiting until "load"

    at /Users/<USER>/DEV/SAAS PactCRM/apps/super-admin/tests/superadmin.test.js:23:16
```

# Test source

```ts
   1 | /**
   2 |  * @file: superadmin.test.js
   3 |  * @description: Автоматизированные тесты для SuperAdmin панели PactCRM
   4 |  * @dependencies: playwright
   5 |  * @created: 2025-01-26
   6 |  */
   7 |
   8 | const { test, expect } = require('@playwright/test');
   9 |
   10 | // Конфигурация тестов
   11 | const BASE_URL = 'http://localhost:3003';
   12 | const TEST_CREDENTIALS = {
   13 |   email: '<EMAIL>',
   14 |   password: 'SuperAdmin123!'
   15 | };
   16 |
   17 | test.describe('SuperAdmin Panel Tests', () => {
   18 |   
   19 |   test('Загрузка страницы авторизации', async ({ page }) => {
   20 |     console.log('🔍 Тест: Загрузка страницы авторизации');
   21 |     
   22 |     // Переходим на страницу авторизации
>  23 |     await page.goto(`${BASE_URL}/admin/login`);
      |                ^ Error: page.goto: net::ERR_ABORTED; maybe frame was detached?
   24 |     
   25 |     // Проверяем заголовок страницы
   26 |     await expect(page).toHaveTitle(/PactCRM/);
   27 |     
   28 |     // Проверяем наличие формы авторизации
   29 |     await expect(page.locator('form')).toBeVisible();
   30 |     
   31 |     // Проверяем наличие полей email и password
   32 |     await expect(page.locator('input[type="email"]')).toBeVisible();
   33 |     await expect(page.locator('input[type="password"]')).toBeVisible();
   34 |     
   35 |     // Проверяем наличие кнопки входа
   36 |     await expect(page.locator('button[type="submit"]')).toBeVisible();
   37 |     
   38 |     console.log('✅ Страница авторизации загружена успешно');
   39 |   });
   40 |
   41 |   test('Ввод тестовых данных и авторизация', async ({ page }) => {
   42 |     console.log('🔍 Тест: Ввод тестовых данных и авторизация');
   43 |     
   44 |     // Переходим на страницу авторизации
   45 |     await page.goto(`${BASE_URL}/admin/login`);
   46 |     
   47 |     // Заполняем форму
   48 |     await page.fill('input[type="email"]', TEST_CREDENTIALS.email);
   49 |     await page.fill('input[type="password"]', TEST_CREDENTIALS.password);
   50 |     
   51 |     // Нажимаем кнопку входа
   52 |     await page.click('button[type="submit"]');
   53 |     
   54 |     // Ждем перенаправления или изменения URL
   55 |     await page.waitForTimeout(3000);
   56 |     
   57 |     console.log('✅ Данные введены, попытка авторизации выполнена');
   58 |   });
   59 |
   60 |   test('Загрузка дашборда', async ({ page }) => {
   61 |     console.log('🔍 Тест: Загрузка дашборда');
   62 |     
   63 |     // Переходим напрямую на дашборд
   64 |     await page.goto(`${BASE_URL}/admin/dashboard`);
   65 |     
   66 |     // Проверяем наличие основных элементов дашборда
   67 |     await expect(page.locator('h1')).toContainText('SuperAdmin Dashboard');
   68 |     
   69 |     // Проверяем наличие статистических карточек
   70 |     const statsCards = page.locator('[data-testid="stats-card"], .grid > div');
   71 |     await expect(statsCards.first()).toBeVisible();
   72 |     
   73 |     console.log('✅ Дашборд загружен успешно');
   74 |   });
   75 |
   76 |   test('Проверка UI элементов дашборда', async ({ page }) => {
   77 |     console.log('🔍 Тест: Проверка UI элементов дашборда');
   78 |     
   79 |     // Переходим на дашборд
   80 |     await page.goto(`${BASE_URL}/admin/dashboard`);
   81 |     
   82 |     // Проверяем наличие боковой панели
   83 |     const sidebar = page.locator('[data-sidebar="sidebar"]');
   84 |     if (await sidebar.isVisible()) {
   85 |       console.log('✅ Боковая панель найдена');
   86 |     }
   87 |     
   88 |     // Проверяем наличие таблиц
   89 |     const tables = page.locator('table');
   90 |     if (await tables.count() > 0) {
   91 |       console.log('✅ Таблицы найдены');
   92 |     }
   93 |     
   94 |     // Проверяем наличие карточек
   95 |     const cards = page.locator('[class*="card"]');
   96 |     if (await cards.count() > 0) {
   97 |       console.log('✅ Карточки найдены');
   98 |     }
   99 |     
  100 |     console.log('✅ UI элементы проверены');
  101 |   });
  102 |
  103 |   test('Проверка консоли на ошибки', async ({ page }) => {
  104 |     console.log('🔍 Тест: Проверка консоли на ошибки');
  105 |     
  106 |     const consoleErrors = [];
  107 |     
  108 |     // Слушаем ошибки консоли
  109 |     page.on('console', msg => {
  110 |       if (msg.type() === 'error') {
  111 |         consoleErrors.push(msg.text());
  112 |       }
  113 |     });
  114 |     
  115 |     // Переходим на страницы
  116 |     await page.goto(`${BASE_URL}/admin/login`);
  117 |     await page.waitForTimeout(2000);
  118 |     
  119 |     await page.goto(`${BASE_URL}/admin/dashboard`);
  120 |     await page.waitForTimeout(2000);
  121 |     
  122 |     // Выводим найденные ошибки
  123 |     if (consoleErrors.length > 0) {
```