/**
 * @file: middleware.ts
 * @description: Middleware для проверки аутентификации клиентов
 * @dependencies: next, @pactcrm/supabase-client
 * @created: 2025-01-26
 */

import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import { createMiddlewareSupabaseClient, getUserRole, hasRouteAccess } from '@pactcrm/supabase-client/dist/middleware/middleware';

/**
 * Middleware для проверки аутентификации клиентов
 *
 * Проверяет наличие сессии и роли пользователя, разрешая доступ только
 * пользователям с ролью client
 *
 * @param req Запрос Next.js
 * @returns Ответ Next.js (перенаправление или продолжение)
 */
export async function middleware(req: NextRequest) {
  const res = NextResponse.next();
  const supabase = createMiddlewareSupabaseClient(req, res);
  const { pathname } = req.nextUrl;

  // Проверяем сессию
  const { data: { session } } = await supabase.auth.getSession();

  // Публичные маршруты для клиентов
  const publicRoutes = ['/client/login', '/client/register'];
  const isPublicRoute = publicRoutes.some(route => pathname.startsWith(route));

  // Если пользователь не аутентифицирован и пытается получить доступ к защищенному маршруту
  if (!session && !isPublicRoute) {
    const redirectUrl = new URL('/client/login', req.url);
    return NextResponse.redirect(redirectUrl);
  }

  // Если пользователь аутентифицирован
  if (session) {
    const role = getUserRole(session);

    // Проверяем, что пользователь имеет роль client
    if (role !== 'client') {
      // Перенаправляем на соответствующую панель в зависимости от роли
      if (role === 'superadmin' || role === 'support') {
        const redirectUrl = new URL('http://localhost:3001/admin/dashboard', req.url);
        return NextResponse.redirect(redirectUrl);
      } else if (role === 'tenant_admin' || role === 'after_sales_manager') {
        const redirectUrl = new URL('http://localhost:3000/dashboard', req.url);
        return NextResponse.redirect(redirectUrl);
      } else {
        // Неизвестная роль - выходим
        const redirectUrl = new URL('/client/login', req.url);
        return NextResponse.redirect(redirectUrl);
      }
    }

    // Если пользователь на странице входа, перенаправляем на панель клиента
    if (isPublicRoute) {
      const redirectUrl = new URL('/client/dashboard', req.url);
      return NextResponse.redirect(redirectUrl);
    }
  }

  return res;
}

/**
 * Конфигурация middleware
 *
 * Указывает, для каких маршрутов должен применяться middleware
 */
export const config = {
  matcher: [
    // Все маршруты клиентов
    '/client/:path*',
  ],
};
