/**
 * @file: middleware.ts
 * @description: Рабочий middleware для SuperAdmin с Supabase аутентификацией
 * @dependencies: next, @supabase/ssr
 * @created: 2025-01-26
 */

import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import { createServerClient } from '@supabase/ssr';

/**
 * Создает клиент Supabase для middleware
 */
function createMiddlewareSupabaseClient(req: NextRequest, res: NextResponse) {
  return createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return req.cookies.get(name)?.value;
        },
        set(name: string, value: string, options: any) {
          res.cookies.set({
            name,
            value,
            ...options,
          });
        },
        remove(name: string, options: any) {
          res.cookies.set({
            name,
            value: '',
            ...options,
          });
        },
      },
    }
  );
}

/**
 * Получает роль пользователя из сессии
 */
function getUserRole(session: any): string | null {
  return session?.user?.user_metadata?.role || null;
}

/**
 * Middleware для проверки аутентификации SuperAdmin
 *
 * Проверяет наличие сессии и роли пользователя, разрешая доступ только
 * пользователям с ролями superadmin и support
 *
 * @param req Запрос Next.js
 * @returns Ответ Next.js (перенаправление или продолжение)
 */
export async function middleware(req: NextRequest) {
  const res = NextResponse.next();
  const { pathname } = req.nextUrl;

  // Публичные маршруты для SuperAdmin
  const publicRoutes = ['/admin/login', '/admin/simple-login', '/test'];
  const isPublicRoute = publicRoutes.some(route => pathname.startsWith(route));

  try {
    // Создаем клиент Supabase
    const supabase = createMiddlewareSupabaseClient(req, res);

    // Получаем сессию
    const { data: { session }, error } = await supabase.auth.getSession();

    // Если ошибка получения сессии, логируем и пропускаем
    if (error) {
      console.warn('SuperAdmin middleware: Error getting session:', error.message);
      if (!isPublicRoute) {
        return NextResponse.redirect(new URL('/admin/login', req.url));
      }
      return res;
    }

    // Если пользователь не аутентифицирован и пытается получить доступ к защищенному маршруту
    if (!session && !isPublicRoute) {
      return NextResponse.redirect(new URL('/admin/login', req.url));
    }

    // Если пользователь аутентифицирован
    if (session) {
      const role = getUserRole(session);

      // Проверяем, что пользователь имеет права SuperAdmin или Support
      if (role !== 'superadmin' && role !== 'support') {
        // Перенаправляем на соответствующую панель в зависимости от роли
        if (role === 'tenant_admin' || role === 'after_sales_manager') {
          return NextResponse.redirect(new URL('http://localhost:3000/dashboard', req.url));
        } else if (role === 'client') {
          return NextResponse.redirect(new URL('http://localhost:3002/client/dashboard', req.url));
        } else {
          // Неизвестная роль - выходим
          return NextResponse.redirect(new URL('/admin/login', req.url));
        }
      }

      // Если пользователь на странице входа, перенаправляем на панель администратора
      if (isPublicRoute && pathname.startsWith('/admin/login')) {
        return NextResponse.redirect(new URL('/admin/dashboard', req.url));
      }
    }

    return res;
  } catch (error) {
    console.error('SuperAdmin middleware: Unexpected error:', error);
    // В случае неожиданной ошибки, пропускаем запрос
    return res;
  }
}

/**
 * Конфигурация middleware
 *
 * Указывает, для каких маршрутов должен применяться middleware
 */
export const config = {
  matcher: [
    // Все маршруты SuperAdmin
    '/admin/:path*',
    // Исключаем статические файлы и API
    '/((?!api|_next/static|_next/image|favicon.ico).*)',
  ],
};
