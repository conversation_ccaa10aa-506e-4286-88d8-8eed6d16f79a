/**
 * @file: middleware.ts
 * @description: Middleware для проверки аутентификации SuperAdmin
 * @dependencies: next, @pactcrm/supabase-client
 * @created: 2025-01-26
 */

import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import { createMiddlewareSupabaseClient, getUserRole, hasRouteAccess } from '@pactcrm/supabase-client/dist/middleware/middleware';

/**
 * Middleware для проверки аутентификации SuperAdmin
 *
 * Проверяет наличие сессии и роли пользователя, разрешая доступ только
 * пользователям с ролями superadmin и support
 *
 * @param req Запрос Next.js
 * @returns Ответ Next.js (перенаправление или продолжение)
 */
export async function middleware(req: NextRequest) {
  const res = NextResponse.next();
  const supabase = createMiddlewareSupabaseClient(req, res);
  const { pathname } = req.nextUrl;

  // Проверяем сессию
  const { data: { session } } = await supabase.auth.getSession();

  // Публичные маршруты для SuperAdmin
  const publicRoutes = ['/admin/login'];
  const isPublicRoute = publicRoutes.some(route => pathname.startsWith(route));

  // Если пользователь не аутентифицирован и пытается получить доступ к защищенному маршруту
  if (!session && !isPublicRoute) {
    const redirectUrl = new URL('/admin/login', req.url);
    return NextResponse.redirect(redirectUrl);
  }

  // Если пользователь аутентифицирован
  if (session) {
    const role = getUserRole(session);

    // Проверяем, что пользователь имеет права SuperAdmin или Support
    if (role !== 'superadmin' && role !== 'support') {
      // Перенаправляем на соответствующую панель в зависимости от роли
      if (role === 'tenant_admin' || role === 'after_sales_manager') {
        return NextResponse.redirect('http://localhost:3000/dashboard');
      } else if (role === 'client') {
        return NextResponse.redirect('http://localhost:3002/client/dashboard');
      } else {
        // Неизвестная роль - выходим
        const redirectUrl = new URL('/admin/login', req.url);
        return NextResponse.redirect(redirectUrl);
      }
    }

    // Если пользователь на странице входа, перенаправляем на панель администратора
    if (isPublicRoute) {
      const redirectUrl = new URL('/admin/dashboard', req.url);
      return NextResponse.redirect(redirectUrl);
    }
  }

  return res;
}

/**
 * Конфигурация middleware
 *
 * Указывает, для каких маршрутов должен применяться middleware
 */
export const config = {
  matcher: [
    // Временно отключаем middleware для отладки
    // '/admin/:path*',
  ],
};
