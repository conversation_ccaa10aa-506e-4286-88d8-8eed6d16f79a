# Test info

- Name: SuperAdmin Panel Tests >> Проверка адаптивности дизайна
- Location: /Users/<USER>/DEV/SAAS PactCRM/apps/super-admin/tests/superadmin.test.js:131:3

# Error details

```
Error: page.goto: net::ERR_ABORTED; maybe frame was detached?
Call log:
  - navigating to "http://localhost:3003/admin/dashboard", waiting until "load"

    at /Users/<USER>/DEV/SAAS PactCRM/apps/super-admin/tests/superadmin.test.js:143:18
```

# Test source

```ts
   43 |     
   44 |     // Переходим на страницу авторизации
   45 |     await page.goto(`${BASE_URL}/admin/login`);
   46 |     
   47 |     // Заполняем форму
   48 |     await page.fill('input[type="email"]', TEST_CREDENTIALS.email);
   49 |     await page.fill('input[type="password"]', TEST_CREDENTIALS.password);
   50 |     
   51 |     // Нажимаем кнопку входа
   52 |     await page.click('button[type="submit"]');
   53 |     
   54 |     // Ждем перенаправления или изменения URL
   55 |     await page.waitForTimeout(3000);
   56 |     
   57 |     console.log('✅ Данные введены, попытка авторизации выполнена');
   58 |   });
   59 |
   60 |   test('Загрузка дашборда', async ({ page }) => {
   61 |     console.log('🔍 Тест: Загрузка дашборда');
   62 |     
   63 |     // Переходим напрямую на дашборд
   64 |     await page.goto(`${BASE_URL}/admin/dashboard`);
   65 |     
   66 |     // Проверяем наличие основных элементов дашборда
   67 |     await expect(page.locator('h1')).toContainText('SuperAdmin Dashboard');
   68 |     
   69 |     // Проверяем наличие статистических карточек
   70 |     const statsCards = page.locator('[data-testid="stats-card"], .grid > div');
   71 |     await expect(statsCards.first()).toBeVisible();
   72 |     
   73 |     console.log('✅ Дашборд загружен успешно');
   74 |   });
   75 |
   76 |   test('Проверка UI элементов дашборда', async ({ page }) => {
   77 |     console.log('🔍 Тест: Проверка UI элементов дашборда');
   78 |     
   79 |     // Переходим на дашборд
   80 |     await page.goto(`${BASE_URL}/admin/dashboard`);
   81 |     
   82 |     // Проверяем наличие боковой панели
   83 |     const sidebar = page.locator('[data-sidebar="sidebar"]');
   84 |     if (await sidebar.isVisible()) {
   85 |       console.log('✅ Боковая панель найдена');
   86 |     }
   87 |     
   88 |     // Проверяем наличие таблиц
   89 |     const tables = page.locator('table');
   90 |     if (await tables.count() > 0) {
   91 |       console.log('✅ Таблицы найдены');
   92 |     }
   93 |     
   94 |     // Проверяем наличие карточек
   95 |     const cards = page.locator('[class*="card"]');
   96 |     if (await cards.count() > 0) {
   97 |       console.log('✅ Карточки найдены');
   98 |     }
   99 |     
  100 |     console.log('✅ UI элементы проверены');
  101 |   });
  102 |
  103 |   test('Проверка консоли на ошибки', async ({ page }) => {
  104 |     console.log('🔍 Тест: Проверка консоли на ошибки');
  105 |     
  106 |     const consoleErrors = [];
  107 |     
  108 |     // Слушаем ошибки консоли
  109 |     page.on('console', msg => {
  110 |       if (msg.type() === 'error') {
  111 |         consoleErrors.push(msg.text());
  112 |       }
  113 |     });
  114 |     
  115 |     // Переходим на страницы
  116 |     await page.goto(`${BASE_URL}/admin/login`);
  117 |     await page.waitForTimeout(2000);
  118 |     
  119 |     await page.goto(`${BASE_URL}/admin/dashboard`);
  120 |     await page.waitForTimeout(2000);
  121 |     
  122 |     // Выводим найденные ошибки
  123 |     if (consoleErrors.length > 0) {
  124 |       console.log('⚠️ Найдены ошибки в консоли:');
  125 |       consoleErrors.forEach(error => console.log(`  - ${error}`));
  126 |     } else {
  127 |       console.log('✅ Ошибок в консоли не найдено');
  128 |     }
  129 |   });
  130 |
  131 |   test('Проверка адаптивности дизайна', async ({ page }) => {
  132 |     console.log('🔍 Тест: Проверка адаптивности дизайна');
  133 |     
  134 |     // Тестируем разные размеры экрана
  135 |     const viewports = [
  136 |       { width: 1920, height: 1080, name: 'Desktop' },
  137 |       { width: 768, height: 1024, name: 'Tablet' },
  138 |       { width: 375, height: 667, name: 'Mobile' }
  139 |     ];
  140 |     
  141 |     for (const viewport of viewports) {
  142 |       await page.setViewportSize({ width: viewport.width, height: viewport.height });
> 143 |       await page.goto(`${BASE_URL}/admin/dashboard`);
      |                  ^ Error: page.goto: net::ERR_ABORTED; maybe frame was detached?
  144 |       await page.waitForTimeout(1000);
  145 |       
  146 |       console.log(`✅ ${viewport.name} (${viewport.width}x${viewport.height}) - OK`);
  147 |     }
  148 |   });
  149 |
  150 |   test('Тестирование упрощенных страниц', async ({ page }) => {
  151 |     console.log('🔍 Тест: Тестирование упрощенных страниц');
  152 |     
  153 |     // Тестируем упрощенную авторизацию
  154 |     await page.goto(`${BASE_URL}/admin/simple-login`);
  155 |     await expect(page.locator('h1')).toContainText('PactCRM SuperAdmin');
  156 |     console.log('✅ Упрощенная авторизация загружена');
  157 |     
  158 |     // Тестируем упрощенный дашборд
  159 |     await page.goto(`${BASE_URL}/admin/simple-dashboard`);
  160 |     await expect(page.locator('h1')).toContainText('SuperAdmin Dashboard');
  161 |     console.log('✅ Упрощенный дашборд загружен');
  162 |     
  163 |     // Тестируем тестовую страницу
  164 |     await page.goto(`${BASE_URL}/test`);
  165 |     await expect(page.locator('h1')).toContainText('SuperAdmin Test Page');
  166 |     console.log('✅ Тестовая страница загружена');
  167 |   });
  168 |
  169 | });
  170 |
```