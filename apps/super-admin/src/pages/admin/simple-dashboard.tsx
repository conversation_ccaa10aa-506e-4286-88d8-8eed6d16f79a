/**
 * @file: simple-dashboard.tsx
 * @description: Упрощенный дашборд без внешних зависимостей
 * @created: 2025-01-26
 */

export default function SimpleDashboardPage() {
  const stats = [
    { title: "Всего компаний", value: "24", icon: "🏢" },
    { title: "Активные пользователи", value: "1,234", icon: "👥" },
    { title: "Общий доход", value: "₾45,231", icon: "💰" },
    { title: "Открытые тикеты", value: "12", icon: "⚠️" }
  ]

  const companies = [
    { name: "ООО \"Строй Инвест\"", plan: "Premium", status: "Активна" },
    { name: "ГК \"Премиум Девелопмент\"", plan: "Business", status: "Активна" },
    { name: "ОО<PERSON> \"Новый Город\"", plan: "Starter", status: "Пробный период" }
  ]

  return (
    <div style={{ 
      minHeight: '100vh',
      backgroundColor: '#f8f9fa',
      fontFamily: 'Arial, sans-serif'
    }}>
      {/* Header */}
      <header style={{
        backgroundColor: 'white',
        padding: '1rem 2rem',
        borderBottom: '1px solid #e9ecef',
        boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
      }}>
        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <h1 style={{ 
            margin: 0, 
            color: '#333',
            fontSize: '24px',
            fontWeight: 'bold'
          }}>
            🛡️ SuperAdmin Dashboard
          </h1>
          <div style={{ color: '#666', fontSize: '14px' }}>
            {new Date().toLocaleDateString('ru-RU')}
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main style={{ padding: '2rem' }}>
        {/* Stats Grid */}
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
          gap: '1rem',
          marginBottom: '2rem'
        }}>
          {stats.map((stat, index) => (
            <div key={index} style={{
              backgroundColor: 'white',
              padding: '1.5rem',
              borderRadius: '8px',
              boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
              border: '1px solid #e9ecef'
            }}>
              <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <div>
                  <p style={{ 
                    margin: '0 0 0.5rem 0', 
                    color: '#666',
                    fontSize: '14px'
                  }}>
                    {stat.title}
                  </p>
                  <p style={{ 
                    margin: 0, 
                    color: '#333',
                    fontSize: '24px',
                    fontWeight: 'bold'
                  }}>
                    {stat.value}
                  </p>
                </div>
                <div style={{ fontSize: '32px' }}>
                  {stat.icon}
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Content Grid */}
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(400px, 1fr))',
          gap: '2rem'
        }}>
          {/* Companies Table */}
          <div style={{
            backgroundColor: 'white',
            padding: '1.5rem',
            borderRadius: '8px',
            boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
            border: '1px solid #e9ecef'
          }}>
            <h2 style={{ 
              margin: '0 0 1rem 0', 
              color: '#333',
              fontSize: '18px',
              fontWeight: 'bold'
            }}>
              📊 Последние компании
            </h2>
            
            <div style={{ overflowX: 'auto' }}>
              <table style={{ width: '100%', borderCollapse: 'collapse' }}>
                <thead>
                  <tr style={{ borderBottom: '2px solid #e9ecef' }}>
                    <th style={{ 
                      padding: '0.75rem', 
                      textAlign: 'left',
                      color: '#666',
                      fontSize: '14px',
                      fontWeight: '500'
                    }}>
                      Компания
                    </th>
                    <th style={{ 
                      padding: '0.75rem', 
                      textAlign: 'left',
                      color: '#666',
                      fontSize: '14px',
                      fontWeight: '500'
                    }}>
                      Тариф
                    </th>
                    <th style={{ 
                      padding: '0.75rem', 
                      textAlign: 'left',
                      color: '#666',
                      fontSize: '14px',
                      fontWeight: '500'
                    }}>
                      Статус
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {companies.map((company, index) => (
                    <tr key={index} style={{ borderBottom: '1px solid #f8f9fa' }}>
                      <td style={{ 
                        padding: '0.75rem',
                        color: '#333',
                        fontSize: '14px'
                      }}>
                        {company.name}
                      </td>
                      <td style={{ 
                        padding: '0.75rem',
                        color: '#333',
                        fontSize: '14px'
                      }}>
                        <span style={{
                          backgroundColor: '#e3f2fd',
                          color: '#1976d2',
                          padding: '0.25rem 0.5rem',
                          borderRadius: '4px',
                          fontSize: '12px'
                        }}>
                          {company.plan}
                        </span>
                      </td>
                      <td style={{ 
                        padding: '0.75rem',
                        color: '#333',
                        fontSize: '14px'
                      }}>
                        <span style={{
                          backgroundColor: company.status === 'Активна' ? '#e8f5e8' : '#fff3cd',
                          color: company.status === 'Активна' ? '#2e7d32' : '#856404',
                          padding: '0.25rem 0.5rem',
                          borderRadius: '4px',
                          fontSize: '12px'
                        }}>
                          {company.status}
                        </span>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>

          {/* System Info */}
          <div style={{
            backgroundColor: 'white',
            padding: '1.5rem',
            borderRadius: '8px',
            boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
            border: '1px solid #e9ecef'
          }}>
            <h2 style={{ 
              margin: '0 0 1rem 0', 
              color: '#333',
              fontSize: '18px',
              fontWeight: 'bold'
            }}>
              🖥️ Системная информация
            </h2>
            
            <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>
              <div>
                <p style={{ margin: '0 0 0.5rem 0', color: '#666', fontSize: '14px' }}>
                  Использование CPU
                </p>
                <div style={{ 
                  backgroundColor: '#f8f9fa', 
                  borderRadius: '4px', 
                  height: '8px',
                  position: 'relative'
                }}>
                  <div style={{
                    backgroundColor: '#28a745',
                    height: '100%',
                    width: '45%',
                    borderRadius: '4px'
                  }}></div>
                </div>
                <p style={{ margin: '0.25rem 0 0 0', color: '#333', fontSize: '14px', fontWeight: 'bold' }}>
                  45%
                </p>
              </div>
              
              <div>
                <p style={{ margin: '0 0 0.5rem 0', color: '#666', fontSize: '14px' }}>
                  Использование RAM
                </p>
                <div style={{ 
                  backgroundColor: '#f8f9fa', 
                  borderRadius: '4px', 
                  height: '8px',
                  position: 'relative'
                }}>
                  <div style={{
                    backgroundColor: '#ffc107',
                    height: '100%',
                    width: '67%',
                    borderRadius: '4px'
                  }}></div>
                </div>
                <p style={{ margin: '0.25rem 0 0 0', color: '#333', fontSize: '14px', fontWeight: 'bold' }}>
                  67%
                </p>
              </div>
              
              <div>
                <p style={{ margin: '0 0 0.5rem 0', color: '#666', fontSize: '14px' }}>
                  База данных
                </p>
                <p style={{ margin: 0, color: '#333', fontSize: '18px', fontWeight: 'bold' }}>
                  23GB
                </p>
                <p style={{ margin: '0.25rem 0 0 0', color: '#666', fontSize: '12px' }}>
                  +2.1GB за последний месяц
                </p>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  )
}
