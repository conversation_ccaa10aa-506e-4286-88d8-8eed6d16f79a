/**
 * @file: auth-config.ts
 * @description: Конфигурация аутентификации Supabase для PactCRM
 * @dependencies: @supabase/auth-helpers-nextjs, @supabase/ssr
 * @created: 2025-01-26
 */

import { createServerClient, type CookieOptions } from '@supabase/ssr'
import { createBrowserClient } from '@supabase/ssr'
import { NextRequest, NextResponse } from 'next/server'
import { cookies } from 'next/headers'

/**
 * Конфигурация для различных типов клиентов Supabase
 */
export const AUTH_CONFIG = {
  // URL и ключи из переменных окружения
  url: process.env.NEXT_PUBLIC_SUPABASE_URL!,
  anonKey: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,

  // Настройки авторизации
  auth: {
    // Автоматическое обновление токенов
    autoRefreshToken: true,
    // Сохранение сессии в localStorage
    persistSession: true,
    // Обнаружение сессии из URL
    detectSessionInUrl: true,
  },

  // Настройки cookies
  cookies: {
    name: 'pactcrm-auth-token',
    lifetime: 60 * 60 * 24 * 7, // 7 дней
    domain: process.env.NODE_ENV === 'production' ? '.pactcrm.com' : 'localhost',
    sameSite: 'lax' as const,
    secure: process.env.NODE_ENV === 'production',
  },

  // URL для перенаправлений
  redirectUrls: {
    signIn: '/dashboard',
    signOut: '/login',
    signUp: '/dashboard',
    resetPassword: '/reset-password',
  },

  // Роли и их приоритеты
  roles: {
    superadmin: { priority: 1, defaultRedirect: '/admin/dashboard' },
    support: { priority: 2, defaultRedirect: '/admin/dashboard' },
    tenant_admin: { priority: 3, defaultRedirect: '/dashboard' },
    after_sales_manager: { priority: 4, defaultRedirect: '/dashboard' },
    client: { priority: 5, defaultRedirect: '/client/dashboard' },
  },
} as const

/**
 * Создает клиент Supabase для браузера
 */
export function createBrowserSupabaseClient() {
  return createBrowserClient(AUTH_CONFIG.url, AUTH_CONFIG.anonKey, {
    auth: AUTH_CONFIG.auth,
  })
}

/**
 * Создает клиент Supabase для сервера
 */
export async function createServerSupabaseClient() {
  const cookieStore = await cookies()

  return createServerClient(AUTH_CONFIG.url, AUTH_CONFIG.anonKey, {
    cookies: {
      get(name: string) {
        return cookieStore.get(name)?.value
      },
      set(name: string, value: string, options: CookieOptions) {
        try {
          cookieStore.set({ name, value, ...options })
        } catch (error) {
          // Игнорируем ошибки установки cookies в middleware
        }
      },
      remove(name: string, options: CookieOptions) {
        try {
          cookieStore.set({ name, value: '', ...options })
        } catch (error) {
          // Игнорируем ошибки удаления cookies в middleware
        }
      },
    },
  })
}

/**
 * Создает клиент Supabase для middleware
 */
export function createMiddlewareSupabaseClient(req: NextRequest, res: NextResponse) {
  return createServerClient(AUTH_CONFIG.url, AUTH_CONFIG.anonKey, {
    cookies: {
      get(name: string) {
        return req.cookies.get(name)?.value
      },
      set(name: string, value: string, options: CookieOptions) {
        res.cookies.set({
          name,
          value,
          ...options,
        })
      },
      remove(name: string, options: CookieOptions) {
        res.cookies.set({
          name,
          value: '',
          ...options,
        })
      },
    },
  })
}

/**
 * Получает роль пользователя из сессии
 */
export function getUserRole(session: any): string | null {
  return session?.user?.user_metadata?.role || null
}

/**
 * Получает tenant_id пользователя из сессии
 */
export function getUserTenantId(session: any): string | null {
  return session?.user?.user_metadata?.tenant_id || null
}

/**
 * Проверяет, имеет ли пользователь доступ к маршруту
 */
export function hasRouteAccess(userRole: string | null, pathname: string): boolean {
  if (!userRole) return false

  // Маршруты для SuperAdmin и Support
  if (pathname.startsWith('/admin')) {
    return userRole === 'superadmin' || userRole === 'support'
  }

  // Маршруты для Tenant Admin и After Sales Manager
  if (pathname.startsWith('/dashboard')) {
    return userRole === 'tenant_admin' || userRole === 'after_sales_manager'
  }

  // Маршруты для клиентов
  if (pathname.startsWith('/client')) {
    return userRole === 'client'
  }

  return true
}

/**
 * Получает URL для перенаправления на основе роли пользователя
 */
export function getRedirectUrl(userRole: string | null): string {
  if (!userRole || !(userRole in AUTH_CONFIG.roles)) {
    return AUTH_CONFIG.redirectUrls.signOut
  }

  return AUTH_CONFIG.roles[userRole as keyof typeof AUTH_CONFIG.roles].defaultRedirect
}

/**
 * Типы для TypeScript
 */
export type UserRole = keyof typeof AUTH_CONFIG.roles
export type AuthConfig = typeof AUTH_CONFIG

/**
 * Константы для использования в приложении
 */
export const ROLE_PRIORITIES = Object.fromEntries(
  Object.entries(AUTH_CONFIG.roles).map(([role, config]) => [role, config.priority])
)

export const DEFAULT_REDIRECTS = Object.fromEntries(
  Object.entries(AUTH_CONFIG.roles).map(([role, config]) => [role, config.defaultRedirect])
)
