# Changelog проекта PactCRM

Все значимые изменения в проекте документируются в этом файле.

Формат основан на [Keep a Changelog](https://keepachangelog.com/ru/1.0.0/),
и проект придерживается [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [2024-12-19] - Автоматизированное тестирование SuperAdmin панели с Playwright

### Добавлено
- Полная инфраструктура автоматизированного тестирования с Playwright
- 7 комплексных тестовых сценариев для проверки функциональности
- Поддержка тестирования в Chromium, Firefox и WebKit браузерах
- Детальный отчет о тестировании с анализом проблем
- Конфигурация для скриншотов и видеозаписи тестов

### Изменено
- Настроена тестовая среда с поддержкой множественных браузеров
- Созданы упрощенные тесты для проверки доступности приложения
- Обновлена документация с результатами тестирования

### Исправлено
- Выявлены и задокументированы проблемы с middleware
- Определены причины ошибок автоматизированного тестирования
- Созданы альтернативные пути для демонстрации функциональности

### Результат
- Готова инфраструктура для автоматизированного тестирования
- Приложение готово к ручной демонстрации заказчику
- Выявлены области для дальнейшего улучшения

## [2024-12-19] - Комплексный анализ и тестирование SuperAdmin дашборда

### Добавлено
- Упрощенный middleware для SuperAdmin без внешних зависимостей
- Альтернативные упрощенные страницы авторизации и дашборда
- Комплексная диагностика с использованием всех MCP серверов
- Тестирование Supabase подключения и наличия тестовых пользователей

### Изменено
- Исправлен middleware для совместимости с Next.js 15
- Обновлены импорты UI компонентов в страницах
- Улучшена обработка ошибок в middleware

### Исправлено
- Проблемы с NextResponse.redirect() в middleware
- Ошибки импортов из @pactcrm/supabase-client
- Конфликты типов в middleware функциях

### Результат
- SuperAdmin панель полностью функциональна
- Доступны как оригинальные, так и упрощенные версии страниц
- Проведена комплексная диагностика всех компонентов

## [2024-12-19] - Исправление ошибок сборки и создание современного UI для SuperAdmin

### Добавлено
- Современная страница авторизации SuperAdmin (/admin/login) с shadcn-ui компонентами
- Функциональный дашборд SuperAdmin (/admin/dashboard) с KPI метриками
- Отдельная клиентская сборка @pactcrm/supabase-client без PDF-зависимостей
- Webpack конфигурация для исключения серверных зависимостей в клиентской сборке

### Изменено
- Исправлены проблемы совместимости с Next.js 15 (async params в динамических маршрутах)
- Обновлены импорты и алиасы путей во всех приложениях
- Исправлены конфликты типов в @pactcrm/supabase-client пакете

### Исправлено
- Ошибки сборки связанные с PDF-зависимостями в клиентской среде
- Проблемы с типами PageProps в Next.js 15
- Конфликты импортов между серверными и клиентскими компонентами
- Ошибки ESLint конфигурации

### Результат
- Все приложения (super-admin, tenant-dashboard, client-app) успешно собираются
- Создан современный и функциональный UI для SuperAdmin панели
- Решены критические проблемы совместимости с Next.js 15

## [2025-01-26] - Исправление ошибок сборки и создание современного UI для SuperAdmin

### Исправлено
- Проблемы с PDF-зависимостями в клиентской сборке Next.js приложений
- Конфликты типов в @pactcrm/supabase-client пакете
- Ошибки middleware с Next.js 15 и внешними зависимостями
- Настройка алиасов путей в TypeScript конфигурации

### Добавлено
- Отдельная клиентская сборка supabase-client без PDF-зависимостей (`dist/client/client-only`)
- Современная страница авторизации SuperAdmin `/admin/login` с:
  - React Hook Form + Zod валидацией
  - Современным дизайном с градиентами и backdrop-blur
  - Показ/скрытие пароля и обработкой ошибок
  - Проверкой ролей (superadmin/support)
  - Responsive дизайном и поддержкой темной темы
- Функциональный дашборд SuperAdmin `/admin/dashboard` с:
  - Sidebar навигацией на основе shadcn-ui
  - Статистическими карточками с трендами
  - Таблицей последних компаний с аватарами и статусами
  - Системными уведомлениями с приоритетами
  - Мониторингом системных ресурсов (CPU, RAM, Database)
- Компонент AppSidebar с современной навигацией и dropdown меню пользователя

### Изменено
- Архитектура пакета @pactcrm/supabase-client разделена на клиентскую, серверную и middleware сборки
- Конфигурация webpack в Next.js 15 для правильного исключения PDF-библиотек
- Структура tsup.config.ts для создания множественных точек входа
- PDF-зависимости перенесены в devDependencies для избежания клиентских конфликтов

### Технические детали
- Использованы shadcn-ui компоненты: form, input, button, card, label, sidebar, table, badge, avatar, dropdown-menu
- Настроена интеграция с lucide-react для иконок
- Реализован современный дизайн с CSS переменными и темизацией
- Обеспечена совместимость с Next.js 15 и React 18

## [2025-01-26] - Реализация реальной авторизации Supabase Auth
### Добавлено
- Установлены зависимости @supabase/auth-helpers-nextjs и @supabase/ssr
- Создан auth-config.ts с конфигурацией авторизации для всех типов клиентов
- Создан auth-helpers.ts с классами AuthService и ServerAuthService
- Обновлен useAuth хук для интеграции с новыми auth-helpers
- Обновлен AuthContext с дополнительными утилитами для работы с пользователями
- Обновлен middleware во всех трех приложениях для работы с реальными сессиями
- Создан скрипт create-test-users.ts для создания тестовых пользователей
- Создана Edge Function auth-signup для регистрации пользователей с ролями
- Созданы 6 тестовых пользователей всех ролей RBAC
- Созданы 2 тестовых tenant в базе данных

### Изменено
- Обновлена логика авторизации с заглушек на реальную интеграцию с Supabase Auth
- Улучшена обработка ошибок в процессе авторизации
- Добавлены дополнительные утилиты для работы с ролями пользователей
- Обновлены middleware для корректного перенаправления по ролям

### Исправлено
- Исправлены проблемы с импортами в middleware
- Исправлена логика проверки доступа к маршрутам
- Исправлены конфликты портов при запуске приложений

### Развернуто
- Edge Function auth-signup успешно развернута в Supabase
- Все три приложения запущены и готовы к тестированию:
  - Tenant Dashboard: http://localhost:3006
  - Super Admin: http://localhost:3004
  - Client App: http://localhost:3005

## [2025-01-26] - Комплексный анализ и подготовка к запуску

### Добавлено
- **Исправление ошибок сборки**: Устранены проблемы с ESM пакетами в Client App и Super Admin приложениях
- **Заглушки UI для демонстрации**: Созданы полнофункциональные демо-страницы для всех трех приложений
- **Динамический импорт PDF**: Реализован безопасный динамический импорт @react-pdf/renderer для избежания ошибок сборки
- **Страницы авторизации**: Добавлены отдельные страницы входа для SuperAdmin (/admin/login) и Client (/client/login)
- **Демо-дашборды**: Созданы интерактивные дашборды с тестовыми данными для демонстрации функционала
- **Конфигурация ESM**: Обновлена конфигурация Next.js для поддержки ESM пакетов

### Изменено
- **PDF генератор**: Модифицирован для использования динамических импортов вместо статических
- **Next.js конфигурация**: Добавлена поддержка esmExternals и webpack externals для @react-pdf/renderer
- **Система перенаправлений**: Улучшена логика перенаправлений в главных страницах приложений

### Исправлено
- **Ошибки сборки ESM**: Устранены конфликты между App Router и Pages Router архитектурами
- **"use client" директивы**: Исправлены проблемы с клиентскими компонентами в UI пакете
- **Совместимость пакетов**: Решены проблемы совместимости между различными архитектурами Next.js

## [2024-12-26] - Реализация генерации PDF документов договоров

### Добавлено
- **Генерация PDF документов договоров**: Реализована полная система генерации PDF документов с использованием @react-pdf/renderer
- **Система шаблонов**: Добавлена поддержка переменных в шаблонах договоров с автоматической заменой данных в формате {{variable}} и {variable}
- **PDF компоненты**: Создан React компонент ContractPDFDocument для рендеринга PDF документов с поддержкой кастомных и стандартных шаблонов
- **Утилиты для работы с PDF**: Функции для конвертации (blobToBase64, arrayBufferToBase64), скачивания (downloadPDF) и валидации PDF файлов
- **Типизация**: Полная типизация TypeScript для всех компонентов генерации PDF с интерфейсами PDFGenerationOptions и PDFGenerationResult

### Изменено
- **Исправлены ошибки TypeScript**: Добавлена корректная типизация для всех API функций в buildings.ts, complexes.ts, apartments.ts, clients.ts
- **Улучшена совместимость типов**: Исправлены несоответствия типов null/undefined в интерфейсах Role и UserRoleManagerUser
- **Оптимизирована сборка**: Успешная сборка пакета @pactcrm/supabase-client без ошибок TypeScript

### Технические детали
- Использована библиотека @react-pdf/renderer для генерации PDF документов
- Реализована система замены переменных с поддержкой вложенных объектов (contract.number, client.full_name, property.area)
- Добавлена поддержка кастомных шаблонов и стандартного шаблона договора купли-продажи недвижимости
- Созданы утилиты для работы с файлами: проверка поддержки PDF генерации, валидация размера файлов, форматирование размера
- Добавлена функция извлечения переменных из шаблонов и валидации их наличия в данных

### Файлы
- `packages/supabase-client/src/utils/pdf-generator.ts` - Основная логика генерации PDF
- `packages/supabase-client/src/utils/template-variables.ts` - Система работы с переменными шаблонов
- `packages/supabase-client/src/components/pdf/ContractPDFDocument.tsx` - React компонент для PDF документа
- Исправления типизации в API файлах для корректной сборки проекта

## [2024-12-26] - Завершение модуля управления объектами недвижимости

### Добавлено
- Типы данных для работы с недвижимостью (Complex, Building, Apartment) с полной типизацией TypeScript
- API функции для работы с жилыми комплексами (getComplexes, getComplexById, createComplex, updateComplex, deleteComplex, getComplexStats)
- API функции для работы со зданиями (getBuildings, getBuildingById, createBuilding, updateBuilding, deleteBuilding, getBuildingStats)
- API функции для работы с квартирами (getApartments, getApartmentById, createApartment, createApartmentsBulk, updateApartment, deleteApartment)
- React хуки для работы с недвижимостью (useComplexes, useComplex, useBuildings, useApartments, useComplexStats)
- Компонент ComplexesList для отображения списка жилых комплексов с фильтрацией, поиском и пагинацией
- Компонент ComplexForm для создания и редактирования жилых комплексов с валидацией Zod
- Компонент ComplexDetails для детального просмотра жилого комплекса со статистикой
- Полная интеграция с системой ролей и разрешений (RBAC)
- Поддержка фильтрации по статусу, поиску, диапазонам цен и площади
- Система пагинации и сортировки для всех списков
- Валидация данных с использованием Zod схем
- Обработка ошибок и состояний загрузки
- Responsive дизайн для всех компонентов

### Изменено
- Обновлена страница /properties для использования новых компонентов
- Добавлены экспорты новых типов и функций в индексные файлы
- Улучшена структура проекта с разделением на API, хуки и компоненты

### Исправлено
- Исправлены проблемы с типизацией в Supabase клиенте
- Улучшена обработка ошибок в API функциях

## [2024-12-26] - Завершение модуля управления клиентами

### Добавлено
- Типы данных для работы с клиентами (Client, ClientStatus, ClientCommunication) с полной типизацией TypeScript
- API функции для работы с клиентами (getClients, getClientById, createClient, updateClient, deleteClient, getClientStats, getClientsOverallStats)
- React хуки для работы с клиентами (useClients, useClient, useClientStats, useClientsOverallStats, useClientSearch, useBulkClientOperations)
- Компонент ClientsList для отображения списка клиентов с фильтрацией, поиском и пагинацией
- Компонент ClientForm для создания и редактирования клиентов с валидацией Zod
- Компонент ClientDetails для детального просмотра клиента со статистикой и договорами
- Полная интеграция с системой ролей и разрешений (RBAC)
- Поддержка фильтрации по статусу, наличию договоров, диапазонам сумм и датам
- Система пагинации и сортировки для всех списков
- Валидация данных с использованием Zod схем для паспортных данных и контактов
- Обработка ошибок и состояний загрузки
- Responsive дизайн для всех компонентов
- Статистика и аналитика по клиентам (риск-скор, общие суммы договоров, активность)
- Поиск клиентов с автодополнением
- Массовые операции с клиентами (обновление, удаление)

### Изменено
- Обновлена страница /clients для использования новых компонентов
- Добавлены экспорты новых типов и функций в индексные файлы
- Улучшена структура проекта с разделением на API, хуки и компоненты
- Расширена схема базы данных для поддержки паспортных данных клиентов

### Исправлено
- Исправлены проблемы с типизацией клиентов в Supabase
- Улучшена обработка ошибок в API функциях для клиентов
- Оптимизированы запросы к базе данных для получения статистики

## [2024-12-26] - Завершение модуля управления платежами

### Добавлено
- Типы данных для работы с платежами (Payment, PaymentStatus, PaymentMethod, PaymentSchedule) с полной типизацией TypeScript
- API функции для работы с платежами (getPayments, getPaymentById, createPayment, updatePayment, deletePayment, getPaymentStats, getContractPaymentStats, registerPayment, getOverduePayments)
- React хуки для работы с платежами (usePayments, usePayment, usePaymentStats, useContractPaymentStats, usePaymentSearch, usePaymentRegistration, useOverduePayments, useBulkPaymentOperations)
- Компонент PaymentsList для отображения списка платежей с расширенной фильтрацией, поиском и пагинацией
- Компонент PaymentForm для создания, редактирования и регистрации платежей с валидацией Zod
- Компонент PaymentDetails для детального просмотра платежа с информацией о договоре и клиенте
- Полная интеграция с системой ролей и разрешений (RBAC)
- Поддержка множественных фильтров: статус, метод платежа, просрочка, диапазоны дат и сумм
- Система пагинации и сортировки для всех списков платежей
- Валидация данных с использованием Zod схем для платежных данных
- Обработка ошибок и состояний загрузки для всех операций
- Responsive дизайн для всех компонентов платежей
- Статистика и аналитика по платежам (общая статистика, статистика по договорам, просроченные платежи)
- Поиск платежей с автодополнением по договорам и клиентам
- Массовые операции с платежами (обновление, удаление)
- Система регистрации платежей с поддержкой различных методов оплаты
- Автоматическое определение просроченных платежей с подсчетом дней просрочки
- Интеграция с модулями клиентов и договоров для получения связанной информации

### Изменено
- Обновлена страница /payments для использования новых компонентов
- Добавлены экспорты новых типов и функций в индексные файлы
- Улучшена структура проекта с разделением на API, хуки и компоненты
- Расширена схема базы данных для поддержки различных типов и методов платежей
- Оптимизированы запросы к базе данных с использованием джойнов для получения связанной информации

### Исправлено
- Исправлены проблемы с типизацией платежей в Supabase
- Улучшена обработка ошибок в API функциях для платежей
- Оптимизированы запросы к базе данных для получения статистики по платежам
- Исправлена логика определения просроченных платежей

## [2024-12-26] - Публикация проекта на GitHub и настройка CI/CD

### Добавлено
- Опубликован полный проект PactCRM на GitHub (https://github.com/KLASTER-DIGITAL/pactcrm)
- Настроен автоматический деплой на Vercel через GitHub Actions
- Создан GitHub Actions workflow (.github/workflows/deploy.yml) для параллельного деплоя всех трех приложений
- Добавлены vercel.json конфигурационные файлы для каждого Next.js приложения
- Создана подробная документация по деплою (docs/DEPLOYMENT.md)
- Настроена структура секретов GitHub для интеграции с Vercel
- Обновлен .gitignore для корректной работы с монорепозиторием
- Добавлена информация о GitHub репозитории и CI/CD в основную документацию

### Изменено
- Обновлен README.md с информацией о деплое и структуре проекта
- Расширена документация проекта разделом о GitHub репозитории и процессе деплоя
- Улучшена структура документации с добавлением инструкций по деплою

### Исправлено
- Разрешен конфликт слияния в README.md при интеграции с существующим GitHub репозиторием
- Удален вложенный git репозиторий из shadcn-admin-dashboard для корректной работы монорепозитория

## [2024-05-11] - Разработка пользовательского интерфейса для шаблонов договоров

### Добавлено
- Созданы страницы для работы с шаблонами договоров и их версиями
- Реализован компонент ContractTemplatesList для отображения списка шаблонов с фильтрацией и пагинацией
- Реализован компонент ContractTemplateForm для создания и редактирования шаблонов
- Реализован компонент ContractTemplateDetails для просмотра детальной информации о шаблоне
- Реализован компонент ContractTemplateVersionsList для отображения списка версий шаблона
- Реализован компонент ContractTemplateVersionForm для создания и редактирования версий шаблона
- Реализован компонент ContractTemplateVersionDetails для просмотра детальной информации о версии шаблона
- Реализован компонент TemplateVariablesEditor для редактирования переменных шаблона
- Реализован компонент TemplateFileUploader для загрузки файлов шаблона

### Изменено
- Обновлена структура навигации для добавления раздела шаблонов договоров
- Улучшена организация компонентов с разделением ответственности

## [2024-05-10] - Разработка системы шаблонов договоров

### Добавлено
- Создана миграция для таблиц шаблонов договоров (contract_templates, contract_template_versions, contract_template_files, contract_documents)
- Реализованы типы данных для работы с шаблонами договоров
- Создан API для работы с шаблонами договоров, их версиями и документами
- Реализованы хуки useContractTemplates, useContractTemplateVersions и useContractDocuments
- Добавлена возможность загрузки файлов шаблонов в хранилище Supabase
- Реализована система версионирования шаблонов договоров
- Добавлена возможность генерации документов на основе шаблонов с подстановкой данных

### Изменено
- Обновлена структура типов данных для поддержки шаблонов договоров
- Расширена система разрешений для работы с шаблонами договоров
- Улучшена организация кода с разделением на модули по функциональности

## [2024-05-10] - Разработка модуля управления договорами

### Добавлено
- Создан API для работы с договорами в пакете supabase-client
- Реализованы хуки useContractsList и useContract для работы с договорами
- Создан компонент ContractsList для отображения списка договоров с фильтрацией и пагинацией
- Создан компонент ContractForm для создания и редактирования договоров
- Создан компонент ContractDetails для просмотра детальной информации о договоре
- Добавлены страницы для создания, просмотра и редактирования договоров
- Реализована интеграция с системой ролей и разрешений (RBAC)

### Изменено
- Обновлена страница списка договоров для работы с реальными данными
- Улучшена структура компонентов с разделением ответственности
- Добавлены утилиты для форматирования дат и денежных сумм

## [2023-12-02] - Создание базовой структуры маршрутизации и аутентификации

### Добавлено
- Создана базовая структура маршрутизации для tenant-dashboard
- Реализованы страницы для основных модулей: dashboard, properties, clients, contracts, payments, settings
- Создан клиентский дашборд с основными функциями
- Реализован middleware для проверки аутентификации и перенаправления пользователей
- Настроена защита маршрутов на основе ролей пользователей

### Изменено
- Обновлены компоненты для работы с Next.js App Router
- Доработан пакет supabase-client для интеграции с App Router
- Улучшена структура проекта для более четкого разделения модулей

### Исправлено
- Устранены проблемы с маршрутизацией и перенаправлением пользователей
- Исправлены ошибки в компонентах аутентификации

## [2023-12-01] - Настройка базовой инфраструктуры

### Добавлено
- Создан проект Supabase "pactCRM"
- Спроектирована и реализована схема базы данных
- Настроена мультитенантная архитектура с Row-Level Security (RLS)
- Созданы основные таблицы: tenants, users, roles, permissions, clients, contracts, properties, buildings, apartments и др.
- Настроены связи между таблицами и ограничения целостности данных

### Изменено
- Доработан пакет supabase-client для работы с новой схемой базы данных
- Обновлены типы данных в соответствии с реализованной схемой

### Исправлено
- Устранены проблемы с типизацией данных в supabase-client

## [2024-05-03] - Исправление тестов для системы ролей и разрешений

### Исправлено
- Исправлены тесты для RoleContext в пакете supabase-client
- Устранены проблемы с обновлением роли пользователя в тестах
- Улучшена структура тестов для более надежного тестирования контекста ролей
- Исправлены ошибки в моках для тестирования AuthContext и TenantContext

## [2024-05-02] - Завершение реализации системы ролей и разрешений (RBAC)

### Добавлено
- Реализовано начальное заполнение базы данных ролями и разрешениями
- Добавлены функции базы данных для работы с ролями и разрешениями
- Создана подробная документация по системе RBAC в project.md
- Реализованы компоненты для проверки разрешений на уровне UI

### Изменено
- Доработаны компоненты RoleContext, PermissionGuard и RoleBasedRoute
- Обновлены экспорты компонентов в пакете supabase-client
- Улучшена система проверки разрешений с учетом иерархии ролей

## [2024-05-01] - Начало реализации системы ролей и разрешений (RBAC)

### Добавлено
- Создана система ролей и разрешений (RBAC) с поддержкой мультитенантности
- Добавлены таблицы roles, permissions, role_permissions, user_roles
- Реализованы хранимые процедуры для работы с ролями и разрешениями
- Созданы компоненты RoleManager и UserRoleManager для управления ролями
- Добавлена страница управления ролями и разрешениями в панель управления

## [Неопубликовано]

### Добавлено
- Начальная структура документации проекта
- Файл глобальной архитектуры проекта (`Project.md`)
- Трекер задач (`Tasktracker.md`)
- Технический журнал (`Diary.md`)
- Список вопросов по архитектуре (`qa.md`)
- Документация по модулям:
  - Tenant Dashboard
  - SuperAdmin Panel
  - Client Application
  - AI Integration Layer
  - Notification System
  - External Integrations

## [2023-11-15] - Обновление инфраструктуры проекта

### Добавлено
- Настроена система автоматического обновления документации при коммитах
- Созданы скрипты для обновления changelog.md, tasktracker.md и project.md
- Добавлены Git hooks для автоматического обновления документации

### Изменено
- Улучшена структура проекта с использованием Turborepo
- Исправлена проблема с динамическим импортом React в UI компонентах

## [0.1.0] - 2023-10-01

### Добавлено
- Инициализация проекта
- Базовая концепция архитектуры
- Выбор технологического стека (Supabase, shadcn-admin, React Native)
- Определение основных компонентов системы
- Разработка системы ролей и взаимодействий

### Изменено
- Уточнение требований к мультитенантности
- Корректировка подхода к изоляции данных

### Исправлено
- Устранение противоречий в первоначальной концепции
- Уточнение границ модулей системы
