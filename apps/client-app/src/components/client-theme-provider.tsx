/**
 * @file: client-theme-provider.tsx
 * @description: Клиентский провайдер темы для приложения клиентов
 * @dependencies: next-themes, react
 * @created: 2025-01-26
 */

"use client";

import { ThemeProvider } from "next-themes";
import { ReactNode } from "react";

interface ClientThemeProviderProps {
  children: ReactNode;
}

export default function ClientThemeProvider({ children }: ClientThemeProviderProps) {
  return (
    <ThemeProvider
      attribute="class"
      defaultTheme="system"
      enableSystem
      disableTransitionOnChange
    >
      {children}
    </ThemeProvider>
  );
}
