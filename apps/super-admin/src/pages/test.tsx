/**
 * @file: test.tsx
 * @description: Простая тестовая страница для проверки работы приложения
 * @created: 2025-01-26
 */

export default function TestPage() {
  return (
    <div style={{ 
      padding: '2rem', 
      fontFamily: 'Arial, sans-serif',
      backgroundColor: '#f5f5f5',
      minHeight: '100vh'
    }}>
      <h1 style={{ color: '#333', marginBottom: '1rem' }}>
        🎉 SuperAdmin Test Page
      </h1>
      
      <div style={{ 
        backgroundColor: 'white', 
        padding: '1.5rem', 
        borderRadius: '8px',
        boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
        marginBottom: '1rem'
      }}>
        <h2 style={{ color: '#666', marginBottom: '1rem' }}>
          ✅ Приложение работает!
        </h2>
        
        <p style={{ marginBottom: '0.5rem' }}>
          <strong>URL:</strong> http://localhost:3003/test
        </p>
        
        <p style={{ marginBottom: '0.5rem' }}>
          <strong>Статус:</strong> <span style={{ color: 'green' }}>Активно</span>
        </p>
        
        <p style={{ marginBottom: '1rem' }}>
          <strong>Время:</strong> {new Date().toLocaleString()}
        </p>
        
        <div style={{ marginTop: '1rem' }}>
          <a 
            href="/admin/login" 
            style={{ 
              display: 'inline-block',
              padding: '0.5rem 1rem',
              backgroundColor: '#007bff',
              color: 'white',
              textDecoration: 'none',
              borderRadius: '4px',
              marginRight: '1rem'
            }}
          >
            Перейти к авторизации
          </a>
          
          <a 
            href="/admin/dashboard" 
            style={{ 
              display: 'inline-block',
              padding: '0.5rem 1rem',
              backgroundColor: '#28a745',
              color: 'white',
              textDecoration: 'none',
              borderRadius: '4px'
            }}
          >
            Перейти к дашборду
          </a>
        </div>
      </div>
      
      <div style={{ 
        backgroundColor: '#fff3cd', 
        padding: '1rem', 
        borderRadius: '4px',
        border: '1px solid #ffeaa7'
      }}>
        <h3 style={{ color: '#856404', margin: '0 0 0.5rem 0' }}>
          📋 Инструкции для тестирования:
        </h3>
        <ol style={{ color: '#856404', margin: 0 }}>
          <li>Убедитесь что эта страница загружается</li>
          <li>Нажмите на кнопку "Перейти к авторизации"</li>
          <li>Проверьте загрузку страницы авторизации</li>
          <li>Нажмите на кнопку "Перейти к дашборду"</li>
          <li>Проверьте загрузку дашборда</li>
        </ol>
      </div>
    </div>
  )
}
