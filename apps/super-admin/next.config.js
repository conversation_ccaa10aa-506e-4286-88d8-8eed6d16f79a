/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  transpilePackages: ["@pactcrm/ui"],
  experimental: {
    esmExternals: 'loose'
  },
  webpack: (config, { isServer }) => {
    // Исключаем @react-pdf/renderer полностью
    config.externals = [
      ...(config.externals || []),
      '@react-pdf/renderer',
      /^@react-pdf\/.*/,
    ];

    // Добавляем fallback для модулей, которые не должны работать в браузере
    config.resolve.fallback = {
      ...config.resolve.fallback,
      fs: false,
      path: false,
      os: false,
      crypto: false,
      stream: false,
      buffer: false,
    };

    // Игнорируем PDF-модули в клиентской сборке
    if (!isServer) {
      config.resolve.alias = {
        ...config.resolve.alias,
        '@react-pdf/renderer': false,
      };
    }

    return config;
  },
};

module.exports = nextConfig;
