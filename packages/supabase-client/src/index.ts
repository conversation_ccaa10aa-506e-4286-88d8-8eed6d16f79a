/**
 * Пакет для работы с Supabase в проекте PactCRM
 *
 * Предоставляет типизированный клиент Supabase, хуки, контексты и утилиты для работы с данными
 *
 * @packageDocumentation
 */

// Клиент Supabase
export * from './supabase';

// Типы данных
export * from './types';

// API функции для работы с данными
export * from './api';

// Хуки для работы с данными
export * from './hooks';

// Контексты для работы с данными
export * from './context';

// Компоненты для работы с данными
export * from './components';

// Утилиты для работы с данными (исключая PDF-генерацию)
export * from './utils/template-variables';
export * from './utils/tenantUtils';

// Клиентские функции аутентификации
export {
  createBrowserSupabaseClient,
  getUserRole,
  getUserTenantId,
  hasRouteAccess,
  getRedirectUrl,
  AUTH_CONFIG,
  ROLE_PRIORITIES,
  DEFAULT_REDIRECTS
} from './auth/auth-client';

export {
  AuthService,
  authService,
  userUtils
} from './auth/auth-helpers';

export type {
  UserRole,
  AuthConfig
} from './auth/auth-client';

export type {
  AuthResult,
  SignUpData,
  SignInData
} from './auth/auth-helpers';
