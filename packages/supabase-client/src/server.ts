/**
 * @file: server.ts
 * @description: Серверные функции для работы с Supabase
 * @dependencies: @supabase/ssr, next/headers
 * @created: 2025-01-26
 */

// Серверные функции аутентификации
export {
  createServerSupabaseClient,
  createMiddlewareSupabaseClient,
  getUserRole,
  getUserTenantId,
  hasRouteAccess,
  getRedirectUrl,
  AUTH_CONFIG,
  ROLE_PRIORITIES,
  DEFAULT_REDIRECTS
} from './auth/auth-config';

export {
  ServerAuthService,
  serverAuthService
} from './auth/auth-server-helpers';

// PDF-генерация (только на сервере)
export * from './utils/pdf-generator';
export * from './components/pdf';

// Типы
export type {
  UserRole,
  AuthConfig
} from './auth/auth-config';
