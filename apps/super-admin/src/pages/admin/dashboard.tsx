/**
 * @file: dashboard.tsx
 * @description: Современный дашборд для SuperAdmin с sidebar
 * @dependencies: shadcn-ui, lucide-react
 * @created: 2025-01-26
 */

"use client"

import { 
  Building2, 
  Users, 
  DollarSign, 
  AlertTriangle,
  TrendingUp,
  Activity,
  Server,
  Database
} from 'lucide-react'

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { SidebarProvider, SidebarInset, SidebarTrigger } from '@/components/ui/sidebar'
import { AppSidebar } from '@/components/app-sidebar'
import { Separator } from '@/components/ui/separator'

// Моковые данные для дашборда
const stats = [
  {
    title: "Всего компаний",
    value: "24",
    change: "+2",
    changeText: "за последний месяц",
    icon: Building2,
    trend: "up"
  },
  {
    title: "Активные пользователи",
    value: "1,234",
    change: "+15%",
    changeText: "от прошлого месяца",
    icon: Users,
    trend: "up"
  },
  {
    title: "Общий доход",
    value: "₾45,231",
    change: "+20%",
    changeText: "от прошлого месяца",
    icon: DollarSign,
    trend: "up"
  },
  {
    title: "Открытые тикеты",
    value: "12",
    change: "-3",
    changeText: "от вчера",
    icon: AlertTriangle,
    trend: "down"
  }
]

const recentCompanies = [
  {
    name: "ООО \"Строй Инвест\"",
    email: "<EMAIL>",
    plan: "Premium",
    status: "active",
    registeredAt: "2 дня назад"
  },
  {
    name: "ГК \"Премиум Девелопмент\"",
    email: "<EMAIL>",
    plan: "Business",
    status: "active",
    registeredAt: "5 дней назад"
  },
  {
    name: "ООО \"Новый Город\"",
    email: "<EMAIL>",
    plan: "Starter",
    status: "trial",
    registeredAt: "1 неделю назад"
  }
]

const systemAlerts = [
  {
    type: "warning",
    message: "Высокая нагрузка на сервер",
    time: "30 минут назад",
    severity: "medium"
  },
  {
    type: "error",
    message: "Ошибка в интеграции с банком TBC",
    time: "2 часа назад",
    severity: "high"
  },
  {
    type: "info",
    message: "Обновление системы завершено",
    time: "6 часов назад",
    severity: "low"
  }
]

export default function SuperAdminDashboard() {
  return (
    <SidebarProvider>
      <AppSidebar />
      <SidebarInset>
        <header className="flex h-16 shrink-0 items-center gap-2 transition-[width,height] ease-linear group-has-[[data-collapsible=icon]]/sidebar-wrapper:h-12">
          <div className="flex items-center gap-2 px-4">
            <SidebarTrigger className="-ml-1" />
            <Separator orientation="vertical" className="mr-2 h-4" />
            <h1 className="text-xl font-semibold">SuperAdmin Dashboard</h1>
          </div>
        </header>
        
        <div className="flex flex-1 flex-col gap-4 p-4 pt-0">
          {/* Статистические карточки */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {stats.map((stat, index) => (
              <Card key={index}>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">{stat.title}</CardTitle>
                  <stat.icon className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{stat.value}</div>
                  <p className="text-xs text-muted-foreground">
                    <span className={`inline-flex items-center ${
                      stat.trend === 'up' ? 'text-green-600' : 'text-red-600'
                    }`}>
                      {stat.trend === 'up' ? <TrendingUp className="h-3 w-3 mr-1" /> : <Activity className="h-3 w-3 mr-1" />}
                      {stat.change}
                    </span>
                    {' '}{stat.changeText}
                  </p>
                </CardContent>
              </Card>
            ))}
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            {/* Последние компании */}
            <Card>
              <CardHeader>
                <CardTitle>Последние компании</CardTitle>
                <CardDescription>Недавно зарегистрированные застройщики</CardDescription>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Компания</TableHead>
                      <TableHead>Тариф</TableHead>
                      <TableHead>Статус</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {recentCompanies.map((company, index) => (
                      <TableRow key={index}>
                        <TableCell>
                          <div className="flex items-center space-x-3">
                            <Avatar className="h-8 w-8">
                              <AvatarImage src={`/avatars/company-${index + 1}.jpg`} />
                              <AvatarFallback>{company.name.charAt(0)}</AvatarFallback>
                            </Avatar>
                            <div>
                              <div className="font-medium">{company.name}</div>
                              <div className="text-sm text-muted-foreground">{company.email}</div>
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge variant="outline">{company.plan}</Badge>
                        </TableCell>
                        <TableCell>
                          <Badge 
                            variant={company.status === 'active' ? 'default' : 'secondary'}
                          >
                            {company.status === 'active' ? 'Активна' : 'Пробный период'}
                          </Badge>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>

            {/* Системные уведомления */}
            <Card>
              <CardHeader>
                <CardTitle>Системные уведомления</CardTitle>
                <CardDescription>Важные события системы</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {systemAlerts.map((alert, index) => (
                    <div key={index} className="flex items-start space-x-3">
                      <div className={`w-2 h-2 rounded-full mt-2 ${
                        alert.severity === 'high' ? 'bg-red-500' :
                        alert.severity === 'medium' ? 'bg-yellow-500' : 'bg-blue-500'
                      }`} />
                      <div className="flex-1">
                        <p className="font-medium text-sm">{alert.message}</p>
                        <p className="text-xs text-muted-foreground">{alert.time}</p>
                      </div>
                      <Badge 
                        variant={alert.severity === 'high' ? 'destructive' : 'secondary'}
                        className="text-xs"
                      >
                        {alert.severity === 'high' ? 'Критично' : 
                         alert.severity === 'medium' ? 'Внимание' : 'Инфо'}
                      </Badge>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Системная информация */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Использование CPU</CardTitle>
                <Server className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">45%</div>
                <div className="w-full bg-secondary rounded-full h-2 mt-2">
                  <div className="bg-primary h-2 rounded-full" style={{ width: '45%' }}></div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Использование RAM</CardTitle>
                <Activity className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">67%</div>
                <div className="w-full bg-secondary rounded-full h-2 mt-2">
                  <div className="bg-yellow-500 h-2 rounded-full" style={{ width: '67%' }}></div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">База данных</CardTitle>
                <Database className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">23GB</div>
                <p className="text-xs text-muted-foreground">
                  +2.1GB за последний месяц
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </SidebarInset>
    </SidebarProvider>
  )
}
