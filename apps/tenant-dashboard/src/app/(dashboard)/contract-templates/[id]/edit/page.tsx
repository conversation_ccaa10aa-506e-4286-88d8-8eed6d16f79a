/**
 * @file: contract-templates/[id]/edit/page.tsx
 * @description: Страница редактирования шаблона договора
 * @dependencies: react, next, @pactcrm/ui, @pactcrm/supabase-client
 * @created: 2024-05-11
 */

import React from 'react';
import { ContractTemplateForm } from '../../components/ContractTemplateForm';
import { PermissionGuard } from '@pactcrm/supabase-client/dist/client/client-only';

interface EditContractTemplatePageProps {
  params: Promise<{
    id: string;
  }>;
}

export default async function EditContractTemplatePage({ params }: EditContractTemplatePageProps) {
  const { id } = await params;
  return (
    <PermissionGuard
      resource="contract_templates"
      action="update"
      fallback={
        <div className="p-8 text-center">
          <h2 className="text-xl font-semibold mb-2">Доступ запрещен</h2>
          <p className="text-muted-foreground">
            У вас нет разрешения на редактирование шаблонов договоров.
          </p>
        </div>
      }
    >
      <div className="space-y-6">
        <h1 className="text-2xl font-bold">Редактирование шаблона договора</h1>
        <ContractTemplateForm id={id} />
      </div>
    </PermissionGuard>
  );
}
