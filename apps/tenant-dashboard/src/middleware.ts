/**
 * @file: middleware.ts
 * @description: Middleware для проверки аутентификации и перенаправления пользователей
 * @dependencies: next, @supabase/auth-helpers-nextjs
 * @created: 2023-12-01
 */

import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import { createMiddlewareSupabaseClient, getUserRole, hasRouteAccess } from '@pactcrm/supabase-client/dist/middleware/middleware';

/**
 * Middleware для проверки аутентификации и перенаправления пользователей
 *
 * Проверяет наличие сессии и роли пользователя, перенаправляя на соответствующие страницы
 * в зависимости от прав доступа
 *
 * @param req Запрос Next.js
 * @returns Ответ Next.js (перенаправление или продолжение)
 */
export async function middleware(req: NextRequest) {
  const res = NextResponse.next();
  const supabase = createMiddlewareSupabaseClient(req, res);
  const { pathname } = req.nextUrl;

  // Проверяем сессию
  const { data: { session } } = await supabase.auth.getSession();

  // Публичные маршруты, доступные без аутентификации
  const publicRoutes = ['/login', '/register', '/reset-password', '/client/login'];
  const isPublicRoute = publicRoutes.some(route => pathname.startsWith(route));

  // Маршруты для клиентов
  const clientRoutes = ['/client/dashboard'];
  const isClientRoute = clientRoutes.some(route => pathname.startsWith(route));

  // Маршруты для администраторов
  const adminRoutes = ['/dashboard', '/properties', '/clients', '/contracts', '/payments', '/settings'];
  const isAdminRoute = adminRoutes.some(route => pathname.startsWith(route));

  // Если пользователь не аутентифицирован и пытается получить доступ к защищенному маршруту
  if (!session && !isPublicRoute) {
    const redirectUrl = new URL('/login', req.url);
    return NextResponse.redirect(redirectUrl);
  }

  // Если пользователь аутентифицирован
  if (session) {
    const role = getUserRole(session);

    // Проверяем доступ к маршруту
    if (!hasRouteAccess(role, pathname)) {
      // Перенаправляем на соответствующую панель в зависимости от роли
      if (role === 'superadmin' || role === 'support') {
        const redirectUrl = new URL('/admin/dashboard', req.url);
        return NextResponse.redirect(redirectUrl);
      } else if (role === 'tenant_admin' || role === 'after_sales_manager') {
        const redirectUrl = new URL('/dashboard', req.url);
        return NextResponse.redirect(redirectUrl);
      } else if (role === 'client') {
        const redirectUrl = new URL('/client/dashboard', req.url);
        return NextResponse.redirect(redirectUrl);
      } else {
        // Неизвестная роль - выходим
        const redirectUrl = new URL('/login', req.url);
        return NextResponse.redirect(redirectUrl);
      }
    }

    // Если пользователь на странице входа, перенаправляем на соответствующую панель
    if (isPublicRoute && pathname !== '/client/login') {
      if (role === 'superadmin' || role === 'support') {
        const redirectUrl = new URL('/admin/dashboard', req.url);
        return NextResponse.redirect(redirectUrl);
      } else if (role === 'tenant_admin' || role === 'after_sales_manager') {
        const redirectUrl = new URL('/dashboard', req.url);
        return NextResponse.redirect(redirectUrl);
      } else if (role === 'client') {
        const redirectUrl = new URL('/client/dashboard', req.url);
        return NextResponse.redirect(redirectUrl);
      }
    }
  }

  return res;
}

/**
 * Конфигурация middleware
 *
 * Указывает, для каких маршрутов должен применяться middleware
 */
export const config = {
  matcher: [
    // Защищенные маршруты
    '/dashboard/:path*',
    '/properties/:path*',
    '/clients/:path*',
    '/contracts/:path*',
    '/payments/:path*',
    '/settings/:path*',
    '/client/dashboard/:path*',
    // Публичные маршруты для проверки перенаправления
    '/login',
    '/register',
    '/reset-password',
    '/client/login',
  ],
};
