/**
 * @file: contract-templates/[id]/versions/new/page.tsx
 * @description: Страница создания новой версии шаблона договора
 * @dependencies: react, next, @pactcrm/ui, @pactcrm/supabase-client
 * @created: 2024-05-11
 */

import React from 'react';
import { ContractTemplateVersionForm } from '../../../components/ContractTemplateVersionForm';
import { PermissionGuard } from '@pactcrm/supabase-client/dist/client/client-only';

interface NewContractTemplateVersionPageProps {
  params: Promise<{
    id: string;
  }>;
}

export default async function NewContractTemplateVersionPage({ params }: NewContractTemplateVersionPageProps) {
  const { id } = await params;
  return (
    <PermissionGuard
      resource="contract_templates"
      action="update"
      fallback={
        <div className="p-8 text-center">
          <h2 className="text-xl font-semibold mb-2">Доступ запрещен</h2>
          <p className="text-muted-foreground">
            У вас нет разрешения на создание версий шаблонов договоров.
          </p>
        </div>
      }
    >
      <div className="space-y-6">
        <h1 className="text-2xl font-bold">Создание новой версии шаблона</h1>
        <p className="text-muted-foreground">
          Создайте новую версию шаблона договора с обновленным содержимым и переменными.
        </p>
        <ContractTemplateVersionForm templateId={id} />
      </div>
    </PermissionGuard>
  );
}
