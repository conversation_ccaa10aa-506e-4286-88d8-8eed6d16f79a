{"name": "@pactcrm/supabase-client", "version": "0.0.0", "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "sideEffects": false, "license": "MIT", "files": ["dist/**"], "scripts": {"build": "tsup", "dev": "tsup --watch", "lint": "eslint \"src/**/*.ts*\"", "clean": "rm -rf .turbo && rm -rf node_modules && rm -rf dist", "test": "vitest run", "test:watch": "vitest", "test:coverage": "vitest run --coverage"}, "dependencies": {"@supabase/auth-helpers-nextjs": "^0.8.7", "@supabase/auth-helpers-react": "^0.4.2", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.39.3"}, "devDependencies": {"@react-pdf/renderer": "^4.3.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/react-hooks": "^8.0.1", "@types/react": "^18.2.48", "@types/react-dom": "^18.2.18", "@vitejs/plugin-react": "^4.5.0", "@vitest/coverage-v8": "^3.1.4", "eslint": "^8.56.0", "eslint-config-custom": "workspace:*", "jsdom": "^26.1.0", "next": "^15.0.0", "react": "^18.2.0", "react-dom": "^18.2.0", "tsconfig": "workspace:*", "tsup": "^8.0.2", "typescript": "^5.3.3", "vitest": "^3.1.4"}, "peerDependencies": {"next": ">=15.0.0", "react": ">=18.0.0", "react-dom": ">=18.0.0"}, "publishConfig": {"access": "public"}}