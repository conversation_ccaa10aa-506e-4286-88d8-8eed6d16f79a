/**
 * @file: contracts/[id]/page.tsx
 * @description: Страница просмотра и редактирования договора
 * @dependencies: react, next, @pactcrm/ui, @pactcrm/supabase-client
 * @created: 2024-05-10
 */

import React from 'react';
import { ContractDetails } from '../components/ContractDetails';
import { PermissionGuard } from '@pactcrm/supabase-client/dist/client/client-only';

interface ContractPageProps {
  params: Promise<{
    id: string;
  }>;
}

export default async function ContractPage({ params }: ContractPageProps) {
  const { id } = await params;
  return (
    <PermissionGuard
      resource="contracts"
      action="read"
      fallback={
        <div className="p-8 text-center">
          <h2 className="text-xl font-semibold mb-2">Доступ запрещен</h2>
          <p className="text-muted-foreground">
            У вас нет разрешения на просмотр договоров.
          </p>
        </div>
      }
    >
      <div className="space-y-6">
        <h1 className="text-2xl font-bold">Договор</h1>
        <ContractDetails id={id} />
      </div>
    </PermissionGuard>
  );
}
