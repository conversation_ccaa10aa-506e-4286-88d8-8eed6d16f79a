{"name": "super-admin", "version": "0.0.0", "private": true, "scripts": {"dev": "next dev --port 3003", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@hookform/resolvers": "^5.0.1", "@pactcrm/supabase-client": "workspace:*", "@pactcrm/ui": "workspace:*", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tooltip": "^1.2.7", "@supabase/auth-helpers-nextjs": "^0.8.7", "@supabase/auth-helpers-react": "^0.4.2", "@supabase/supabase-js": "^2.39.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.344.0", "next": "^15.0.0", "next-themes": "^0.2.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.56.4", "tailwind-merge": "^3.3.0", "zod": "^3.25.28"}, "devDependencies": {"@types/node": "^20.11.5", "@types/react": "^18.2.48", "@types/react-dom": "^18.2.18", "autoprefixer": "^10.4.16", "eslint": "^8.56.0", "eslint-config-custom": "workspace:*", "postcss": "^8.4.32", "tailwindcss": "^4.0.0", "tailwindcss-animate": "^1.0.7", "tsconfig": "workspace:*", "tw-animate-css": "^1.3.0", "typescript": "^5.3.3"}}