/**
 * @file: app-sidebar.tsx
 * @description: Sidebar для SuperAdmin панели
 * @dependencies: shadcn-ui, lucide-react
 * @created: 2025-01-26
 */

"use client"

import {
  Building2,
  Users,
  FileText,
  CreditCard,
  Settings,
  BarChart3,
  Shield,
  Bell,
  Database,
  ChevronUp,
  User2,
} from "lucide-react"

import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
} from "@/components/ui/sidebar"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"

// Данные для навигации
const data = {
  user: {
    name: "<PERSON><PERSON><PERSON><PERSON>",
    email: "<EMAIL>",
    avatar: "/avatars/admin.jpg",
  },
  navMain: [
    {
      title: "Дашборд",
      url: "/admin/dashboard",
      icon: BarChart3,
    },
    {
      title: "Компании",
      url: "/admin/tenants",
      icon: Building2,
      items: [
        {
          title: "Все компании",
          url: "/admin/tenants",
        },
        {
          title: "Добавить компанию",
          url: "/admin/tenants/new",
        },
        {
          title: "Тарифы",
          url: "/admin/tenants/plans",
        },
      ],
    },
    {
      title: "Пользователи",
      url: "/admin/users",
      icon: Users,
      items: [
        {
          title: "Все пользователи",
          url: "/admin/users",
        },
        {
          title: "Роли и права",
          url: "/admin/users/roles",
        },
        {
          title: "Активность",
          url: "/admin/users/activity",
        },
      ],
    },
    {
      title: "Система",
      url: "/admin/system",
      icon: Database,
      items: [
        {
          title: "Мониторинг",
          url: "/admin/system/monitoring",
        },
        {
          title: "Логи",
          url: "/admin/system/logs",
        },
        {
          title: "Резервные копии",
          url: "/admin/system/backups",
        },
      ],
    },
    {
      title: "Безопасность",
      url: "/admin/security",
      icon: Shield,
      items: [
        {
          title: "Аудит",
          url: "/admin/security/audit",
        },
        {
          title: "Блокировки",
          url: "/admin/security/blocks",
        },
        {
          title: "API ключи",
          url: "/admin/security/api-keys",
        },
      ],
    },
  ],
  navSecondary: [
    {
      title: "Уведомления",
      url: "/admin/notifications",
      icon: Bell,
    },
    {
      title: "Настройки",
      url: "/admin/settings",
      icon: Settings,
    },
  ],
}

export function AppSidebar() {
  return (
    <Sidebar variant="inset">
      <SidebarHeader>
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton size="lg" asChild>
              <a href="/admin/dashboard">
                <div className="flex aspect-square size-8 items-center justify-center rounded-lg bg-gradient-to-br from-blue-600 to-purple-600 text-sidebar-primary-foreground">
                  <Shield className="size-4" />
                </div>
                <div className="grid flex-1 text-left text-sm leading-tight">
                  <span className="truncate font-semibold">PactCRM</span>
                  <span className="truncate text-xs">SuperAdmin</span>
                </div>
              </a>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarHeader>
      
      <SidebarContent>
        <SidebarGroup>
          <SidebarGroupLabel>Управление платформой</SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu>
              {data.navMain.map((item) => (
                <SidebarMenuItem key={item.title}>
                  <SidebarMenuButton asChild>
                    <a href={item.url}>
                      <item.icon />
                      <span>{item.title}</span>
                    </a>
                  </SidebarMenuButton>
                  {item.items?.length ? (
                    <SidebarMenuSub>
                      {item.items.map((subItem) => (
                        <SidebarMenuSubItem key={subItem.title}>
                          <SidebarMenuSubButton asChild>
                            <a href={subItem.url}>
                              <span>{subItem.title}</span>
                            </a>
                          </SidebarMenuSubButton>
                        </SidebarMenuSubItem>
                      ))}
                    </SidebarMenuSub>
                  ) : null}
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
        
        <SidebarGroup className="mt-auto">
          <SidebarGroupContent>
            <SidebarMenu>
              {data.navSecondary.map((item) => (
                <SidebarMenuItem key={item.title}>
                  <SidebarMenuButton asChild size="sm">
                    <a href={item.url}>
                      <item.icon />
                      <span>{item.title}</span>
                    </a>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>
      
      <SidebarFooter>
        <SidebarMenu>
          <SidebarMenuItem>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <SidebarMenuButton
                  size="lg"
                  className="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
                >
                  <Avatar className="h-8 w-8 rounded-lg">
                    <AvatarImage src={data.user.avatar} alt={data.user.name} />
                    <AvatarFallback className="rounded-lg">SA</AvatarFallback>
                  </Avatar>
                  <div className="grid flex-1 text-left text-sm leading-tight">
                    <span className="truncate font-semibold">{data.user.name}</span>
                    <span className="truncate text-xs">{data.user.email}</span>
                  </div>
                  <ChevronUp className="ml-auto size-4" />
                </SidebarMenuButton>
              </DropdownMenuTrigger>
              <DropdownMenuContent
                className="w-[--radix-dropdown-menu-trigger-width] min-w-56 rounded-lg"
                side="bottom"
                align="end"
                sideOffset={4}
              >
                <DropdownMenuItem>
                  <User2 />
                  Профиль
                </DropdownMenuItem>
                <DropdownMenuItem>
                  <Settings />
                  Настройки
                </DropdownMenuItem>
                <DropdownMenuItem>
                  Выйти
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarFooter>
    </Sidebar>
  )
}
