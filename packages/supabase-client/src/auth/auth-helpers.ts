/**
 * @file: auth-helpers.ts
 * @description: Вспомогательные функции для аутентификации
 * @dependencies: @supabase/supabase-js
 * @created: 2025-01-26
 */

import { AuthError, User, Session } from '@supabase/supabase-js'
import { createBrowserSupabaseClient, getUserRole, getUserTenantId } from './auth-client'

/**
 * Интерфейс для результата аутентификации
 */
export interface AuthResult {
  user: User | null
  session: Session | null
  error: AuthError | null
}

/**
 * Интерфейс для данных регистрации
 */
export interface SignUpData {
  email: string
  password: string
  role: string
  tenant_id?: string
  first_name?: string
  last_name?: string
  phone?: string
}

/**
 * Интерфейс для данных входа
 */
export interface SignInData {
  email: string
  password: string
}

/**
 * Класс для работы с аутентификацией
 */
export class AuthService {
  private supabase = createBrowserSupabaseClient()

  /**
   * Вход пользователя
   */
  async signIn({ email, password }: SignInData): Promise<AuthResult> {
    try {
      const { data, error } = await this.supabase.auth.signInWithPassword({
        email,
        password,
      })

      if (error) {
        return { user: null, session: null, error }
      }

      return {
        user: data.user,
        session: data.session,
        error: null,
      }
    } catch (error) {
      return {
        user: null,
        session: null,
        error: error as AuthError,
      }
    }
  }

  /**
   * Регистрация пользователя
   */
  async signUp(signUpData: SignUpData): Promise<AuthResult> {
    try {
      const { email, password, role, tenant_id, first_name, last_name, phone } = signUpData

      const { data, error } = await this.supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            role,
            tenant_id,
            first_name,
            last_name,
            phone,
          },
        },
      })

      if (error) {
        return { user: null, session: null, error }
      }

      return {
        user: data.user,
        session: data.session,
        error: null,
      }
    } catch (error) {
      return {
        user: null,
        session: null,
        error: error as AuthError,
      }
    }
  }

  /**
   * Выход пользователя
   */
  async signOut(): Promise<{ error: AuthError | null }> {
    try {
      const { error } = await this.supabase.auth.signOut()
      return { error }
    } catch (error) {
      return { error: error as AuthError }
    }
  }

  /**
   * Сброс пароля
   */
  async resetPassword(email: string): Promise<{ error: AuthError | null }> {
    try {
      const { error } = await this.supabase.auth.resetPasswordForEmail(email, {
        redirectTo: `${window.location.origin}/reset-password`,
      })
      return { error }
    } catch (error) {
      return { error: error as AuthError }
    }
  }

  /**
   * Обновление пароля
   */
  async updatePassword(password: string): Promise<{ error: AuthError | null }> {
    try {
      const { error } = await this.supabase.auth.updateUser({ password })
      return { error }
    } catch (error) {
      return { error: error as AuthError }
    }
  }

  /**
   * Получение текущей сессии
   */
  async getSession(): Promise<{ session: Session | null; error: AuthError | null }> {
    try {
      const { data, error } = await this.supabase.auth.getSession()
      return { session: data.session, error }
    } catch (error) {
      return { session: null, error: error as AuthError }
    }
  }

  /**
   * Получение текущего пользователя
   */
  async getUser(): Promise<{ user: User | null; error: AuthError | null }> {
    try {
      const { data, error } = await this.supabase.auth.getUser()
      return { user: data.user, error }
    } catch (error) {
      return { user: null, error: error as AuthError }
    }
  }

  /**
   * Подписка на изменения аутентификации
   */
  onAuthStateChange(callback: (event: string, session: Session | null) => void) {
    return this.supabase.auth.onAuthStateChange(callback)
  }
}



/**
 * Утилиты для работы с пользователями
 */
export const userUtils = {
  /**
   * Получение полного имени пользователя
   */
  getFullName(user: User | null): string {
    if (!user?.user_metadata) return 'Пользователь'

    const { first_name, last_name } = user.user_metadata
    if (first_name && last_name) {
      return `${first_name} ${last_name}`
    }
    if (first_name) return first_name
    if (last_name) return last_name
    return user.email || 'Пользователь'
  },

  /**
   * Получение роли пользователя
   */
  getRole(user: User | null): string | null {
    return getUserRole({ user })
  },

  /**
   * Получение tenant_id пользователя
   */
  getTenantId(user: User | null): string | null {
    return getUserTenantId({ user })
  },

  /**
   * Проверка роли пользователя
   */
  hasRole(user: User | null, role: string): boolean {
    return this.getRole(user) === role
  },

  /**
   * Проверка, является ли пользователь администратором
   */
  isAdmin(user: User | null): boolean {
    const role = this.getRole(user)
    return role === 'superadmin' || role === 'support' || role === 'tenant_admin'
  },

  /**
   * Проверка, является ли пользователь суперадминистратором
   */
  isSuperAdmin(user: User | null): boolean {
    return this.getRole(user) === 'superadmin'
  },

  /**
   * Проверка, принадлежит ли пользователь к определенному tenant
   */
  belongsToTenant(user: User | null, tenantId: string): boolean {
    const userTenantId = this.getTenantId(user)
    return userTenantId === tenantId
  },
}

/**
 * Экспорт экземпляров сервисов
 */
export const authService = new AuthService()
