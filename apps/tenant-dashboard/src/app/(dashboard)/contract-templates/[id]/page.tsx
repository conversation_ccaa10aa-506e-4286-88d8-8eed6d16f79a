/**
 * @file: contract-templates/[id]/page.tsx
 * @description: Страница просмотра и редактирования шаблона договора
 * @dependencies: react, next, @pactcrm/ui, @pactcrm/supabase-client
 * @created: 2024-05-11
 */

import React from 'react';
import { ContractTemplateDetails } from '../components/ContractTemplateDetails';
import { PermissionGuard } from '@pactcrm/supabase-client/dist/client/client-only';

interface ContractTemplatePageProps {
  params: Promise<{
    id: string;
  }>;
}

export default async function ContractTemplatePage({ params }: ContractTemplatePageProps) {
  const { id } = await params;
  return (
    <PermissionGuard
      resource="contract_templates"
      action="read"
      fallback={
        <div className="p-8 text-center">
          <h2 className="text-xl font-semibold mb-2">Доступ запрещен</h2>
          <p className="text-muted-foreground">
            У вас нет разрешения на просмотр шаблонов договоров.
          </p>
        </div>
      }
    >
      <div className="space-y-6">
        <h1 className="text-2xl font-bold">Шаблон договора</h1>
        <ContractTemplateDetails id={id} />
      </div>
    </PermissionGuard>
  );
}
