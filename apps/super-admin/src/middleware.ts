/**
 * @file: middleware.ts
 * @description: Упрощенный middleware для SuperAdmin без внешних зависимостей
 * @dependencies: next
 * @created: 2025-01-26
 */

import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

/**
 * Middleware для проверки аутентификации SuperAdmin
 *
 * Проверяет наличие сессии и роли пользователя, разрешая доступ только
 * пользователям с ролями superadmin и support
 *
 * @param req Запрос Next.js
 * @returns Ответ Next.js (перенаправление или продолжение)
 */
export async function middleware(req: NextRequest) {
  const { pathname } = req.nextUrl;

  // Публичные маршруты для SuperAdmin
  const publicRoutes = ['/admin/login', '/admin/simple-login', '/test'];
  const isPublicRoute = publicRoutes.some(route => pathname.startsWith(route));

  // Пока что просто логируем и пропускаем все запросы
  console.log(`SuperAdmin middleware: ${pathname}, isPublic: ${isPublicRoute}`);

  return NextResponse.next();
}

/**
 * Конфигурация middleware
 *
 * Указывает, для каких маршрутов должен применяться middleware
 */
export const config = {
  matcher: [
    // Все маршруты SuperAdmin
    '/admin/:path*',
  ],
};
