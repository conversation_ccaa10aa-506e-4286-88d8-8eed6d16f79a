/**
 * @file: use-toast.ts
 * @description: Заглушка для совместимости с sonner
 * @dependencies: sonner
 * @created: 2025-01-26
 */

import { toast } from "sonner"

export const useToast = () => {
  return {
    toast: (options: any) => {
      if (typeof options === 'string') {
        toast(options)
      } else {
        const { title, description, variant } = options
        if (variant === 'destructive') {
          toast.error(title, { description })
        } else {
          toast.success(title, { description })
        }
      }
    }
  }
}

export { toast }
