/**
 * @file: client-only.ts
 * @description: Клиентские функции без PDF-зависимостей
 * @dependencies: @supabase/ssr, react
 * @created: 2025-01-26
 */

// Контексты для работы с данными (основные)
export { SupabaseProvider } from './context/SupabaseProvider';
export { AuthProvider, useAuth } from './context/AuthContext';
export { TenantProvider, useTenant } from './context/TenantContext';
export { RoleProvider, useRole } from './context/RoleContext';

// Клиентские функции аутентификации
export {
  createBrowserSupabaseClient,
  getUserRole,
  getUserTenantId,
  hasRouteAccess,
  getRedirectUrl,
  AUTH_CONFIG,
  ROLE_PRIORITIES,
  DEFAULT_REDIRECTS
} from './auth/auth-client';

export {
  AuthService,
  authService,
  userUtils
} from './auth/auth-helpers';

// Компоненты безопасности
export * from './components/PermissionGuard';
export * from './components/RoleBasedRoute';
export * from './components/RoleManager';
export * from './components/UserRoleManager';

// Утилиты (без PDF)
export * from './utils/template-variables';
export * from './utils/tenantUtils';
