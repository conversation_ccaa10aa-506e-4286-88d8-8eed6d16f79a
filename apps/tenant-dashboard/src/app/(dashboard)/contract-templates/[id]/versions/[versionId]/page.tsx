/**
 * @file: contract-templates/[id]/versions/[versionId]/page.tsx
 * @description: Страница просмотра версии шаблона договора
 * @dependencies: react, next, @pactcrm/ui, @pactcrm/supabase-client
 * @created: 2024-05-11
 */

import React from 'react';
import { ContractTemplateVersionDetails } from '../../../components/ContractTemplateVersionDetails';
import { PermissionGuard } from '@pactcrm/supabase-client/dist/client/client-only';

interface ContractTemplateVersionPageProps {
  params: Promise<{
    id: string;
    versionId: string;
  }>;
}

export default async function ContractTemplateVersionPage({ params }: ContractTemplateVersionPageProps) {
  const { id, versionId } = await params;
  return (
    <PermissionGuard
      resource="contract_templates"
      action="read"
      fallback={
        <div className="p-8 text-center">
          <h2 className="text-xl font-semibold mb-2">Доступ запрещен</h2>
          <p className="text-muted-foreground">
            У вас нет разрешения на просмотр версий шаблонов договоров.
          </p>
        </div>
      }
    >
      <div className="space-y-6">
        <h1 className="text-2xl font-bold">Версия шаблона договора</h1>
        <ContractTemplateVersionDetails
          templateId={id}
          versionId={versionId}
        />
      </div>
    </PermissionGuard>
  );
}
